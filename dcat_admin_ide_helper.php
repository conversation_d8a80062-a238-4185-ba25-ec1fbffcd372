<?php

/**
 * A helper file for Dcat Admin, to provide autocomplete information to your IDE
 *
 * This file should not be included in your code, only analyzed by your IDE!
 *
 * <AUTHOR> <<EMAIL>>
 */
namespace Dcat\Admin {
    use Illuminate\Support\Collection;

    /**
     * @property Grid\Column|Collection id
     * @property Grid\Column|Collection hotal_name
     * @property Grid\Column|Collection activity_type
     * @property Grid\Column|Collection name_en
     * @property Grid\Column|Collection name
     * @property Grid\Column|Collection address
     * @property Grid\Column|Collection is_show
     * @property Grid\Column|Collection web_desc
     * @property Grid\Column|Collection key_words
     * @property Grid\Column|Collection page_title
     * @property Grid\Column|Collection activity_link
     * @property Grid\Column|Collection rate
     * @property Grid\Column|Collection access_code
     * @property Grid\Column|Collection status
     * @property Grid\Column|Collection line_type
     * @property Grid\Column|Collection url
     * @property Grid\Column|Collection button_text
     * @property Grid\Column|Collection date
     * @property Grid\Column|Collection img_url
     * @property Grid\Column|Collection img_url_f
     * @property Grid\Column|Collection content
     * @property Grid\Column|Collection order
     * @property Grid\Column|Collection user_id
     * @property Grid\Column|Collection deleted_at
     * @property Grid\Column|Collection created_at
     * @property Grid\Column|Collection updated_at
     * @property Grid\Column|Collection type
     * @property Grid\Column|Collection version
     * @property Grid\Column|Collection detail
     * @property Grid\Column|Collection is_enabled
     * @property Grid\Column|Collection parent_id
     * @property Grid\Column|Collection icon
     * @property Grid\Column|Collection uri
     * @property Grid\Column|Collection extension
     * @property Grid\Column|Collection permission_id
     * @property Grid\Column|Collection menu_id
     * @property Grid\Column|Collection slug
     * @property Grid\Column|Collection http_method
     * @property Grid\Column|Collection http_path
     * @property Grid\Column|Collection role_id
     * @property Grid\Column|Collection value
     * @property Grid\Column|Collection username
     * @property Grid\Column|Collection password
     * @property Grid\Column|Collection avatar
     * @property Grid\Column|Collection email
     * @property Grid\Column|Collection wx_openid
     * @property Grid\Column|Collection remember_token
     * @property Grid\Column|Collection order_num
     * @property Grid\Column|Collection code
     * @property Grid\Column|Collection key
     * @property Grid\Column|Collection expiration
     * @property Grid\Column|Collection owner
     * @property Grid\Column|Collection city_code
     * @property Grid\Column|Collection country_code
     * @property Grid\Column|Collection city_name
     * @property Grid\Column|Collection city_name_en
     * @property Grid\Column|Collection group
     * @property Grid\Column|Collection sort
     * @property Grid\Column|Collection image
     * @property Grid\Column|Collection country_name
     * @property Grid\Column|Collection country_name_en
     * @property Grid\Column|Collection country_name_pinyin
     * @property Grid\Column|Collection continent_name_en
     * @property Grid\Column|Collection brief_en
     * @property Grid\Column|Collection brief
     * @property Grid\Column|Collection detail_en
     * @property Grid\Column|Collection introducing
     * @property Grid\Column|Collection introducing_en
     * @property Grid\Column|Collection image_url
     * @property Grid\Column|Collection country_url
     * @property Grid\Column|Collection country_link
     * @property Grid\Column|Collection uuid
     * @property Grid\Column|Collection connection
     * @property Grid\Column|Collection queue
     * @property Grid\Column|Collection payload
     * @property Grid\Column|Collection exception
     * @property Grid\Column|Collection failed_at
     * @property Grid\Column|Collection version_no
     * @property Grid\Column|Collection hotelcode
     * @property Grid\Column|Collection hotel_type
     * @property Grid\Column|Collection region
     * @property Grid\Column|Collection hotel_name
     * @property Grid\Column|Collection hotel_name_en
     * @property Grid\Column|Collection hotel_name_pinyin
     * @property Grid\Column|Collection gradient_color
     * @property Grid\Column|Collection primary_currency
     * @property Grid\Column|Collection new_hotel_start_date
     * @property Grid\Column|Collection new_hotel_end_date
     * @property Grid\Column|Collection synxis_hotel_id
     * @property Grid\Column|Collection synxis_chain_id
     * @property Grid\Column|Collection sabre_tax_setup
     * @property Grid\Column|Collection online_status
     * @property Grid\Column|Collection address_en
     * @property Grid\Column|Collection postal_code
     * @property Grid\Column|Collection phone
     * @property Grid\Column|Collection fax
     * @property Grid\Column|Collection zero_rate_code
     * @property Grid\Column|Collection phone_country_code
     * @property Grid\Column|Collection phone_area_code
     * @property Grid\Column|Collection is_up_pay
     * @property Grid\Column|Collection offered_service
     * @property Grid\Column|Collection longitude
     * @property Grid\Column|Collection latitude
     * @property Grid\Column|Collection hotel_desc
     * @property Grid\Column|Collection condition_code
     * @property Grid\Column|Collection remark
     * @property Grid\Column|Collection floor_rate
     * @property Grid\Column|Collection highlight
     * @property Grid\Column|Collection award_winning_dinning
     * @property Grid\Column|Collection website_url
     * @property Grid\Column|Collection unique_quelities_en
     * @property Grid\Column|Collection unique_quelities
     * @property Grid\Column|Collection rooms_total_number
     * @property Grid\Column|Collection floors_number
     * @property Grid\Column|Collection restaurants_number
     * @property Grid\Column|Collection bars_number
     * @property Grid\Column|Collection hasPools
     * @property Grid\Column|Collection michelinStarredRestaurants
     * @property Grid\Column|Collection edit_time
     * @property Grid\Column|Collection trust_online
     * @property Grid\Column|Collection hotel_link
     * @property Grid\Column|Collection hotel_alias_en
     * @property Grid\Column|Collection crs_api
     * @property Grid\Column|Collection check_in_after
     * @property Grid\Column|Collection check_out_before
     * @property Grid\Column|Collection brand_code
     * @property Grid\Column|Collection brand_nameEn
     * @property Grid\Column|Collection brand_name
     * @property Grid\Column|Collection max_childrenage
     * @property Grid\Column|Collection open_date
     * @property Grid\Column|Collection extend
     * @property Grid\Column|Collection images
     * @property Grid\Column|Collection destinations
     * @property Grid\Column|Collection interests
     * @property Grid\Column|Collection eco_certificates
     * @property Grid\Column|Collection feature
     * @property Grid\Column|Collection new_hotel
     * @property Grid\Column|Collection categories
     * @property Grid\Column|Collection neighborhood_tag
     * @property Grid\Column|Collection title_en
     * @property Grid\Column|Collection country_code_en
     * @property Grid\Column|Collection city_code_en
     * @property Grid\Column|Collection logo
     * @property Grid\Column|Collection logo_svg
     * @property Grid\Column|Collection card_logo
     * @property Grid\Column|Collection location_id
     * @property Grid\Column|Collection hotel_code
     * @property Grid\Column|Collection sub_title
     * @property Grid\Column|Collection sub_title_en
     * @property Grid\Column|Collection cuisine
     * @property Grid\Column|Collection website
     * @property Grid\Column|Collection reservation_url
     * @property Grid\Column|Collection menu_1_url
     * @property Grid\Column|Collection menu_2_url
     * @property Grid\Column|Collection start_time
     * @property Grid\Column|Collection end_time
     * @property Grid\Column|Collection dining_id
     * @property Grid\Column|Collection original_id
     * @property Grid\Column|Collection external_id
     * @property Grid\Column|Collection title_cn
     * @property Grid\Column|Collection hotels
     * @property Grid\Column|Collection related_rate_codes
     * @property Grid\Column|Collection start_date
     * @property Grid\Column|Collection end_date
     * @property Grid\Column|Collection stay_start_date
     * @property Grid\Column|Collection stay_end_date
     * @property Grid\Column|Collection rate_code1
     * @property Grid\Column|Collection rate_code2
     * @property Grid\Column|Collection rate_code3
     * @property Grid\Column|Collection rate_code4
     * @property Grid\Column|Collection rate_checked
     * @property Grid\Column|Collection featured
     * @property Grid\Column|Collection offer_types
     * @property Grid\Column|Collection promo_type
     * @property Grid\Column|Collection promo_code
     * @property Grid\Column|Collection description_en
     * @property Grid\Column|Collection distribution
     * @property Grid\Column|Collection highlights
     * @property Grid\Column|Collection offerIncludes
     * @property Grid\Column|Collection offerIncludes_en
     * @property Grid\Column|Collection terms_conditions
     * @property Grid\Column|Collection terms_conditions_en
     * @property Grid\Column|Collection taxes
     * @property Grid\Column|Collection taxes_en
     * @property Grid\Column|Collection members_only
     * @property Grid\Column|Collection percentage_off
     * @property Grid\Column|Collection total_jobs
     * @property Grid\Column|Collection pending_jobs
     * @property Grid\Column|Collection failed_jobs
     * @property Grid\Column|Collection failed_job_ids
     * @property Grid\Column|Collection cancelled_at
     * @property Grid\Column|Collection finished_at
     * @property Grid\Column|Collection attempts
     * @property Grid\Column|Collection reserved_at
     * @property Grid\Column|Collection available_at
     * @property Grid\Column|Collection msg
     * @property Grid\Column|Collection author
     * @property Grid\Column|Collection token
     * @property Grid\Column|Collection tokenable_type
     * @property Grid\Column|Collection tokenable_id
     * @property Grid\Column|Collection abilities
     * @property Grid\Column|Collection last_used_at
     * @property Grid\Column|Collection expires_at
     * @property Grid\Column|Collection brandCode
     * @property Grid\Column|Collection roomcode
     * @property Grid\Column|Collection room_id
     * @property Grid\Column|Collection synxis_room_id
     * @property Grid\Column|Collection size
     * @property Grid\Column|Collection room_name
     * @property Grid\Column|Collection room_name_en
     * @property Grid\Column|Collection room_desc
     * @property Grid\Column|Collection room_desc_en
     * @property Grid\Column|Collection booker_text
     * @property Grid\Column|Collection booker_text_en
     * @property Grid\Column|Collection free_wifi
     * @property Grid\Column|Collection bedtype
     * @property Grid\Column|Collection room_class
     * @property Grid\Column|Collection max_adults
     * @property Grid\Column|Collection max_children
     * @property Grid\Column|Collection max_occupancy
     * @property Grid\Column|Collection rollaway_count
     * @property Grid\Column|Collection is_online
     * @property Grid\Column|Collection selling_order
     * @property Grid\Column|Collection updatetime
     * @property Grid\Column|Collection rma_code
     * @property Grid\Column|Collection booker_text_lang
     * @property Grid\Column|Collection room_name_en_new
     * @property Grid\Column|Collection booker_text_en_new
     * @property Grid\Column|Collection app_name
     * @property Grid\Column|Collection admin_id
     * @property Grid\Column|Collection attr_name
     * @property Grid\Column|Collection attr_type
     * @property Grid\Column|Collection attr_value
     * @property Grid\Column|Collection collect
     * @property Grid\Column|Collection first_name
     * @property Grid\Column|Collection last_name
     * @property Grid\Column|Collection email_verified_at
     * @property Grid\Column|Collection city
     * @property Grid\Column|Collection country
     * @property Grid\Column|Collection is_message
     * @property Grid\Column|Collection is_auth
     * @property Grid\Column|Collection profile_id
     * @property Grid\Column|Collection membership_card_no
     * @property Grid\Column|Collection wellness_id
     *
     * @method Grid\Column|Collection id(string $label = null)
     * @method Grid\Column|Collection hotal_name(string $label = null)
     * @method Grid\Column|Collection activity_type(string $label = null)
     * @method Grid\Column|Collection name_en(string $label = null)
     * @method Grid\Column|Collection name(string $label = null)
     * @method Grid\Column|Collection address(string $label = null)
     * @method Grid\Column|Collection is_show(string $label = null)
     * @method Grid\Column|Collection web_desc(string $label = null)
     * @method Grid\Column|Collection key_words(string $label = null)
     * @method Grid\Column|Collection page_title(string $label = null)
     * @method Grid\Column|Collection activity_link(string $label = null)
     * @method Grid\Column|Collection rate(string $label = null)
     * @method Grid\Column|Collection access_code(string $label = null)
     * @method Grid\Column|Collection status(string $label = null)
     * @method Grid\Column|Collection line_type(string $label = null)
     * @method Grid\Column|Collection url(string $label = null)
     * @method Grid\Column|Collection button_text(string $label = null)
     * @method Grid\Column|Collection date(string $label = null)
     * @method Grid\Column|Collection img_url(string $label = null)
     * @method Grid\Column|Collection img_url_f(string $label = null)
     * @method Grid\Column|Collection content(string $label = null)
     * @method Grid\Column|Collection order(string $label = null)
     * @method Grid\Column|Collection user_id(string $label = null)
     * @method Grid\Column|Collection deleted_at(string $label = null)
     * @method Grid\Column|Collection created_at(string $label = null)
     * @method Grid\Column|Collection updated_at(string $label = null)
     * @method Grid\Column|Collection type(string $label = null)
     * @method Grid\Column|Collection version(string $label = null)
     * @method Grid\Column|Collection detail(string $label = null)
     * @method Grid\Column|Collection is_enabled(string $label = null)
     * @method Grid\Column|Collection parent_id(string $label = null)
     * @method Grid\Column|Collection icon(string $label = null)
     * @method Grid\Column|Collection uri(string $label = null)
     * @method Grid\Column|Collection extension(string $label = null)
     * @method Grid\Column|Collection permission_id(string $label = null)
     * @method Grid\Column|Collection menu_id(string $label = null)
     * @method Grid\Column|Collection slug(string $label = null)
     * @method Grid\Column|Collection http_method(string $label = null)
     * @method Grid\Column|Collection http_path(string $label = null)
     * @method Grid\Column|Collection role_id(string $label = null)
     * @method Grid\Column|Collection value(string $label = null)
     * @method Grid\Column|Collection username(string $label = null)
     * @method Grid\Column|Collection password(string $label = null)
     * @method Grid\Column|Collection avatar(string $label = null)
     * @method Grid\Column|Collection email(string $label = null)
     * @method Grid\Column|Collection wx_openid(string $label = null)
     * @method Grid\Column|Collection remember_token(string $label = null)
     * @method Grid\Column|Collection order_num(string $label = null)
     * @method Grid\Column|Collection code(string $label = null)
     * @method Grid\Column|Collection key(string $label = null)
     * @method Grid\Column|Collection expiration(string $label = null)
     * @method Grid\Column|Collection owner(string $label = null)
     * @method Grid\Column|Collection city_code(string $label = null)
     * @method Grid\Column|Collection country_code(string $label = null)
     * @method Grid\Column|Collection city_name(string $label = null)
     * @method Grid\Column|Collection city_name_en(string $label = null)
     * @method Grid\Column|Collection group(string $label = null)
     * @method Grid\Column|Collection sort(string $label = null)
     * @method Grid\Column|Collection image(string $label = null)
     * @method Grid\Column|Collection country_name(string $label = null)
     * @method Grid\Column|Collection country_name_en(string $label = null)
     * @method Grid\Column|Collection country_name_pinyin(string $label = null)
     * @method Grid\Column|Collection continent_name_en(string $label = null)
     * @method Grid\Column|Collection brief_en(string $label = null)
     * @method Grid\Column|Collection brief(string $label = null)
     * @method Grid\Column|Collection detail_en(string $label = null)
     * @method Grid\Column|Collection introducing(string $label = null)
     * @method Grid\Column|Collection introducing_en(string $label = null)
     * @method Grid\Column|Collection image_url(string $label = null)
     * @method Grid\Column|Collection country_url(string $label = null)
     * @method Grid\Column|Collection country_link(string $label = null)
     * @method Grid\Column|Collection uuid(string $label = null)
     * @method Grid\Column|Collection connection(string $label = null)
     * @method Grid\Column|Collection queue(string $label = null)
     * @method Grid\Column|Collection payload(string $label = null)
     * @method Grid\Column|Collection exception(string $label = null)
     * @method Grid\Column|Collection failed_at(string $label = null)
     * @method Grid\Column|Collection version_no(string $label = null)
     * @method Grid\Column|Collection hotelcode(string $label = null)
     * @method Grid\Column|Collection hotel_type(string $label = null)
     * @method Grid\Column|Collection region(string $label = null)
     * @method Grid\Column|Collection hotel_name(string $label = null)
     * @method Grid\Column|Collection hotel_name_en(string $label = null)
     * @method Grid\Column|Collection hotel_name_pinyin(string $label = null)
     * @method Grid\Column|Collection gradient_color(string $label = null)
     * @method Grid\Column|Collection primary_currency(string $label = null)
     * @method Grid\Column|Collection new_hotel_start_date(string $label = null)
     * @method Grid\Column|Collection new_hotel_end_date(string $label = null)
     * @method Grid\Column|Collection synxis_hotel_id(string $label = null)
     * @method Grid\Column|Collection synxis_chain_id(string $label = null)
     * @method Grid\Column|Collection sabre_tax_setup(string $label = null)
     * @method Grid\Column|Collection online_status(string $label = null)
     * @method Grid\Column|Collection address_en(string $label = null)
     * @method Grid\Column|Collection postal_code(string $label = null)
     * @method Grid\Column|Collection phone(string $label = null)
     * @method Grid\Column|Collection fax(string $label = null)
     * @method Grid\Column|Collection zero_rate_code(string $label = null)
     * @method Grid\Column|Collection phone_country_code(string $label = null)
     * @method Grid\Column|Collection phone_area_code(string $label = null)
     * @method Grid\Column|Collection is_up_pay(string $label = null)
     * @method Grid\Column|Collection offered_service(string $label = null)
     * @method Grid\Column|Collection longitude(string $label = null)
     * @method Grid\Column|Collection latitude(string $label = null)
     * @method Grid\Column|Collection hotel_desc(string $label = null)
     * @method Grid\Column|Collection condition_code(string $label = null)
     * @method Grid\Column|Collection remark(string $label = null)
     * @method Grid\Column|Collection floor_rate(string $label = null)
     * @method Grid\Column|Collection highlight(string $label = null)
     * @method Grid\Column|Collection award_winning_dinning(string $label = null)
     * @method Grid\Column|Collection website_url(string $label = null)
     * @method Grid\Column|Collection unique_quelities_en(string $label = null)
     * @method Grid\Column|Collection unique_quelities(string $label = null)
     * @method Grid\Column|Collection rooms_total_number(string $label = null)
     * @method Grid\Column|Collection floors_number(string $label = null)
     * @method Grid\Column|Collection restaurants_number(string $label = null)
     * @method Grid\Column|Collection bars_number(string $label = null)
     * @method Grid\Column|Collection hasPools(string $label = null)
     * @method Grid\Column|Collection michelinStarredRestaurants(string $label = null)
     * @method Grid\Column|Collection edit_time(string $label = null)
     * @method Grid\Column|Collection trust_online(string $label = null)
     * @method Grid\Column|Collection hotel_link(string $label = null)
     * @method Grid\Column|Collection hotel_alias_en(string $label = null)
     * @method Grid\Column|Collection crs_api(string $label = null)
     * @method Grid\Column|Collection check_in_after(string $label = null)
     * @method Grid\Column|Collection check_out_before(string $label = null)
     * @method Grid\Column|Collection brand_code(string $label = null)
     * @method Grid\Column|Collection brand_nameEn(string $label = null)
     * @method Grid\Column|Collection brand_name(string $label = null)
     * @method Grid\Column|Collection max_childrenage(string $label = null)
     * @method Grid\Column|Collection open_date(string $label = null)
     * @method Grid\Column|Collection extend(string $label = null)
     * @method Grid\Column|Collection images(string $label = null)
     * @method Grid\Column|Collection destinations(string $label = null)
     * @method Grid\Column|Collection interests(string $label = null)
     * @method Grid\Column|Collection eco_certificates(string $label = null)
     * @method Grid\Column|Collection feature(string $label = null)
     * @method Grid\Column|Collection new_hotel(string $label = null)
     * @method Grid\Column|Collection categories(string $label = null)
     * @method Grid\Column|Collection neighborhood_tag(string $label = null)
     * @method Grid\Column|Collection title_en(string $label = null)
     * @method Grid\Column|Collection country_code_en(string $label = null)
     * @method Grid\Column|Collection city_code_en(string $label = null)
     * @method Grid\Column|Collection logo(string $label = null)
     * @method Grid\Column|Collection logo_svg(string $label = null)
     * @method Grid\Column|Collection card_logo(string $label = null)
     * @method Grid\Column|Collection location_id(string $label = null)
     * @method Grid\Column|Collection hotel_code(string $label = null)
     * @method Grid\Column|Collection sub_title(string $label = null)
     * @method Grid\Column|Collection sub_title_en(string $label = null)
     * @method Grid\Column|Collection cuisine(string $label = null)
     * @method Grid\Column|Collection website(string $label = null)
     * @method Grid\Column|Collection reservation_url(string $label = null)
     * @method Grid\Column|Collection menu_1_url(string $label = null)
     * @method Grid\Column|Collection menu_2_url(string $label = null)
     * @method Grid\Column|Collection start_time(string $label = null)
     * @method Grid\Column|Collection end_time(string $label = null)
     * @method Grid\Column|Collection dining_id(string $label = null)
     * @method Grid\Column|Collection original_id(string $label = null)
     * @method Grid\Column|Collection external_id(string $label = null)
     * @method Grid\Column|Collection title_cn(string $label = null)
     * @method Grid\Column|Collection hotels(string $label = null)
     * @method Grid\Column|Collection related_rate_codes(string $label = null)
     * @method Grid\Column|Collection start_date(string $label = null)
     * @method Grid\Column|Collection end_date(string $label = null)
     * @method Grid\Column|Collection stay_start_date(string $label = null)
     * @method Grid\Column|Collection stay_end_date(string $label = null)
     * @method Grid\Column|Collection rate_code1(string $label = null)
     * @method Grid\Column|Collection rate_code2(string $label = null)
     * @method Grid\Column|Collection rate_code3(string $label = null)
     * @method Grid\Column|Collection rate_code4(string $label = null)
     * @method Grid\Column|Collection rate_checked(string $label = null)
     * @method Grid\Column|Collection featured(string $label = null)
     * @method Grid\Column|Collection offer_types(string $label = null)
     * @method Grid\Column|Collection promo_type(string $label = null)
     * @method Grid\Column|Collection promo_code(string $label = null)
     * @method Grid\Column|Collection description_en(string $label = null)
     * @method Grid\Column|Collection distribution(string $label = null)
     * @method Grid\Column|Collection highlights(string $label = null)
     * @method Grid\Column|Collection offerIncludes(string $label = null)
     * @method Grid\Column|Collection offerIncludes_en(string $label = null)
     * @method Grid\Column|Collection terms_conditions(string $label = null)
     * @method Grid\Column|Collection terms_conditions_en(string $label = null)
     * @method Grid\Column|Collection taxes(string $label = null)
     * @method Grid\Column|Collection taxes_en(string $label = null)
     * @method Grid\Column|Collection members_only(string $label = null)
     * @method Grid\Column|Collection percentage_off(string $label = null)
     * @method Grid\Column|Collection total_jobs(string $label = null)
     * @method Grid\Column|Collection pending_jobs(string $label = null)
     * @method Grid\Column|Collection failed_jobs(string $label = null)
     * @method Grid\Column|Collection failed_job_ids(string $label = null)
     * @method Grid\Column|Collection cancelled_at(string $label = null)
     * @method Grid\Column|Collection finished_at(string $label = null)
     * @method Grid\Column|Collection attempts(string $label = null)
     * @method Grid\Column|Collection reserved_at(string $label = null)
     * @method Grid\Column|Collection available_at(string $label = null)
     * @method Grid\Column|Collection msg(string $label = null)
     * @method Grid\Column|Collection author(string $label = null)
     * @method Grid\Column|Collection token(string $label = null)
     * @method Grid\Column|Collection tokenable_type(string $label = null)
     * @method Grid\Column|Collection tokenable_id(string $label = null)
     * @method Grid\Column|Collection abilities(string $label = null)
     * @method Grid\Column|Collection last_used_at(string $label = null)
     * @method Grid\Column|Collection expires_at(string $label = null)
     * @method Grid\Column|Collection brandCode(string $label = null)
     * @method Grid\Column|Collection roomcode(string $label = null)
     * @method Grid\Column|Collection room_id(string $label = null)
     * @method Grid\Column|Collection synxis_room_id(string $label = null)
     * @method Grid\Column|Collection size(string $label = null)
     * @method Grid\Column|Collection room_name(string $label = null)
     * @method Grid\Column|Collection room_name_en(string $label = null)
     * @method Grid\Column|Collection room_desc(string $label = null)
     * @method Grid\Column|Collection room_desc_en(string $label = null)
     * @method Grid\Column|Collection booker_text(string $label = null)
     * @method Grid\Column|Collection booker_text_en(string $label = null)
     * @method Grid\Column|Collection free_wifi(string $label = null)
     * @method Grid\Column|Collection bedtype(string $label = null)
     * @method Grid\Column|Collection room_class(string $label = null)
     * @method Grid\Column|Collection max_adults(string $label = null)
     * @method Grid\Column|Collection max_children(string $label = null)
     * @method Grid\Column|Collection max_occupancy(string $label = null)
     * @method Grid\Column|Collection rollaway_count(string $label = null)
     * @method Grid\Column|Collection is_online(string $label = null)
     * @method Grid\Column|Collection selling_order(string $label = null)
     * @method Grid\Column|Collection updatetime(string $label = null)
     * @method Grid\Column|Collection rma_code(string $label = null)
     * @method Grid\Column|Collection booker_text_lang(string $label = null)
     * @method Grid\Column|Collection room_name_en_new(string $label = null)
     * @method Grid\Column|Collection booker_text_en_new(string $label = null)
     * @method Grid\Column|Collection app_name(string $label = null)
     * @method Grid\Column|Collection admin_id(string $label = null)
     * @method Grid\Column|Collection attr_name(string $label = null)
     * @method Grid\Column|Collection attr_type(string $label = null)
     * @method Grid\Column|Collection attr_value(string $label = null)
     * @method Grid\Column|Collection collect(string $label = null)
     * @method Grid\Column|Collection first_name(string $label = null)
     * @method Grid\Column|Collection last_name(string $label = null)
     * @method Grid\Column|Collection email_verified_at(string $label = null)
     * @method Grid\Column|Collection city(string $label = null)
     * @method Grid\Column|Collection country(string $label = null)
     * @method Grid\Column|Collection is_message(string $label = null)
     * @method Grid\Column|Collection is_auth(string $label = null)
     * @method Grid\Column|Collection profile_id(string $label = null)
     * @method Grid\Column|Collection membership_card_no(string $label = null)
     * @method Grid\Column|Collection wellness_id(string $label = null)
     */
    class Grid {}

    class MiniGrid extends Grid {}

    /**
     * @property Show\Field|Collection id
     * @property Show\Field|Collection hotal_name
     * @property Show\Field|Collection activity_type
     * @property Show\Field|Collection name_en
     * @property Show\Field|Collection name
     * @property Show\Field|Collection address
     * @property Show\Field|Collection is_show
     * @property Show\Field|Collection web_desc
     * @property Show\Field|Collection key_words
     * @property Show\Field|Collection page_title
     * @property Show\Field|Collection activity_link
     * @property Show\Field|Collection rate
     * @property Show\Field|Collection access_code
     * @property Show\Field|Collection status
     * @property Show\Field|Collection line_type
     * @property Show\Field|Collection url
     * @property Show\Field|Collection button_text
     * @property Show\Field|Collection date
     * @property Show\Field|Collection img_url
     * @property Show\Field|Collection img_url_f
     * @property Show\Field|Collection content
     * @property Show\Field|Collection order
     * @property Show\Field|Collection user_id
     * @property Show\Field|Collection deleted_at
     * @property Show\Field|Collection created_at
     * @property Show\Field|Collection updated_at
     * @property Show\Field|Collection type
     * @property Show\Field|Collection version
     * @property Show\Field|Collection detail
     * @property Show\Field|Collection is_enabled
     * @property Show\Field|Collection parent_id
     * @property Show\Field|Collection icon
     * @property Show\Field|Collection uri
     * @property Show\Field|Collection extension
     * @property Show\Field|Collection permission_id
     * @property Show\Field|Collection menu_id
     * @property Show\Field|Collection slug
     * @property Show\Field|Collection http_method
     * @property Show\Field|Collection http_path
     * @property Show\Field|Collection role_id
     * @property Show\Field|Collection value
     * @property Show\Field|Collection username
     * @property Show\Field|Collection password
     * @property Show\Field|Collection avatar
     * @property Show\Field|Collection email
     * @property Show\Field|Collection wx_openid
     * @property Show\Field|Collection remember_token
     * @property Show\Field|Collection order_num
     * @property Show\Field|Collection code
     * @property Show\Field|Collection key
     * @property Show\Field|Collection expiration
     * @property Show\Field|Collection owner
     * @property Show\Field|Collection city_code
     * @property Show\Field|Collection country_code
     * @property Show\Field|Collection city_name
     * @property Show\Field|Collection city_name_en
     * @property Show\Field|Collection group
     * @property Show\Field|Collection sort
     * @property Show\Field|Collection image
     * @property Show\Field|Collection country_name
     * @property Show\Field|Collection country_name_en
     * @property Show\Field|Collection country_name_pinyin
     * @property Show\Field|Collection continent_name_en
     * @property Show\Field|Collection brief_en
     * @property Show\Field|Collection brief
     * @property Show\Field|Collection detail_en
     * @property Show\Field|Collection introducing
     * @property Show\Field|Collection introducing_en
     * @property Show\Field|Collection image_url
     * @property Show\Field|Collection country_url
     * @property Show\Field|Collection country_link
     * @property Show\Field|Collection uuid
     * @property Show\Field|Collection connection
     * @property Show\Field|Collection queue
     * @property Show\Field|Collection payload
     * @property Show\Field|Collection exception
     * @property Show\Field|Collection failed_at
     * @property Show\Field|Collection version_no
     * @property Show\Field|Collection hotelcode
     * @property Show\Field|Collection hotel_type
     * @property Show\Field|Collection region
     * @property Show\Field|Collection hotel_name
     * @property Show\Field|Collection hotel_name_en
     * @property Show\Field|Collection hotel_name_pinyin
     * @property Show\Field|Collection gradient_color
     * @property Show\Field|Collection primary_currency
     * @property Show\Field|Collection new_hotel_start_date
     * @property Show\Field|Collection new_hotel_end_date
     * @property Show\Field|Collection synxis_hotel_id
     * @property Show\Field|Collection synxis_chain_id
     * @property Show\Field|Collection sabre_tax_setup
     * @property Show\Field|Collection online_status
     * @property Show\Field|Collection address_en
     * @property Show\Field|Collection postal_code
     * @property Show\Field|Collection phone
     * @property Show\Field|Collection fax
     * @property Show\Field|Collection zero_rate_code
     * @property Show\Field|Collection phone_country_code
     * @property Show\Field|Collection phone_area_code
     * @property Show\Field|Collection is_up_pay
     * @property Show\Field|Collection offered_service
     * @property Show\Field|Collection longitude
     * @property Show\Field|Collection latitude
     * @property Show\Field|Collection hotel_desc
     * @property Show\Field|Collection condition_code
     * @property Show\Field|Collection remark
     * @property Show\Field|Collection floor_rate
     * @property Show\Field|Collection highlight
     * @property Show\Field|Collection award_winning_dinning
     * @property Show\Field|Collection website_url
     * @property Show\Field|Collection unique_quelities_en
     * @property Show\Field|Collection unique_quelities
     * @property Show\Field|Collection rooms_total_number
     * @property Show\Field|Collection floors_number
     * @property Show\Field|Collection restaurants_number
     * @property Show\Field|Collection bars_number
     * @property Show\Field|Collection hasPools
     * @property Show\Field|Collection michelinStarredRestaurants
     * @property Show\Field|Collection edit_time
     * @property Show\Field|Collection trust_online
     * @property Show\Field|Collection hotel_link
     * @property Show\Field|Collection hotel_alias_en
     * @property Show\Field|Collection crs_api
     * @property Show\Field|Collection check_in_after
     * @property Show\Field|Collection check_out_before
     * @property Show\Field|Collection brand_code
     * @property Show\Field|Collection brand_nameEn
     * @property Show\Field|Collection brand_name
     * @property Show\Field|Collection max_childrenage
     * @property Show\Field|Collection open_date
     * @property Show\Field|Collection extend
     * @property Show\Field|Collection images
     * @property Show\Field|Collection destinations
     * @property Show\Field|Collection interests
     * @property Show\Field|Collection eco_certificates
     * @property Show\Field|Collection feature
     * @property Show\Field|Collection new_hotel
     * @property Show\Field|Collection categories
     * @property Show\Field|Collection neighborhood_tag
     * @property Show\Field|Collection title_en
     * @property Show\Field|Collection country_code_en
     * @property Show\Field|Collection city_code_en
     * @property Show\Field|Collection logo
     * @property Show\Field|Collection logo_svg
     * @property Show\Field|Collection card_logo
     * @property Show\Field|Collection location_id
     * @property Show\Field|Collection hotel_code
     * @property Show\Field|Collection sub_title
     * @property Show\Field|Collection sub_title_en
     * @property Show\Field|Collection cuisine
     * @property Show\Field|Collection website
     * @property Show\Field|Collection reservation_url
     * @property Show\Field|Collection menu_1_url
     * @property Show\Field|Collection menu_2_url
     * @property Show\Field|Collection start_time
     * @property Show\Field|Collection end_time
     * @property Show\Field|Collection dining_id
     * @property Show\Field|Collection original_id
     * @property Show\Field|Collection external_id
     * @property Show\Field|Collection title_cn
     * @property Show\Field|Collection hotels
     * @property Show\Field|Collection related_rate_codes
     * @property Show\Field|Collection start_date
     * @property Show\Field|Collection end_date
     * @property Show\Field|Collection stay_start_date
     * @property Show\Field|Collection stay_end_date
     * @property Show\Field|Collection rate_code1
     * @property Show\Field|Collection rate_code2
     * @property Show\Field|Collection rate_code3
     * @property Show\Field|Collection rate_code4
     * @property Show\Field|Collection rate_checked
     * @property Show\Field|Collection featured
     * @property Show\Field|Collection offer_types
     * @property Show\Field|Collection promo_type
     * @property Show\Field|Collection promo_code
     * @property Show\Field|Collection description_en
     * @property Show\Field|Collection distribution
     * @property Show\Field|Collection highlights
     * @property Show\Field|Collection offerIncludes
     * @property Show\Field|Collection offerIncludes_en
     * @property Show\Field|Collection terms_conditions
     * @property Show\Field|Collection terms_conditions_en
     * @property Show\Field|Collection taxes
     * @property Show\Field|Collection taxes_en
     * @property Show\Field|Collection members_only
     * @property Show\Field|Collection percentage_off
     * @property Show\Field|Collection total_jobs
     * @property Show\Field|Collection pending_jobs
     * @property Show\Field|Collection failed_jobs
     * @property Show\Field|Collection failed_job_ids
     * @property Show\Field|Collection cancelled_at
     * @property Show\Field|Collection finished_at
     * @property Show\Field|Collection attempts
     * @property Show\Field|Collection reserved_at
     * @property Show\Field|Collection available_at
     * @property Show\Field|Collection msg
     * @property Show\Field|Collection author
     * @property Show\Field|Collection token
     * @property Show\Field|Collection tokenable_type
     * @property Show\Field|Collection tokenable_id
     * @property Show\Field|Collection abilities
     * @property Show\Field|Collection last_used_at
     * @property Show\Field|Collection expires_at
     * @property Show\Field|Collection brandCode
     * @property Show\Field|Collection roomcode
     * @property Show\Field|Collection room_id
     * @property Show\Field|Collection synxis_room_id
     * @property Show\Field|Collection size
     * @property Show\Field|Collection room_name
     * @property Show\Field|Collection room_name_en
     * @property Show\Field|Collection room_desc
     * @property Show\Field|Collection room_desc_en
     * @property Show\Field|Collection booker_text
     * @property Show\Field|Collection booker_text_en
     * @property Show\Field|Collection free_wifi
     * @property Show\Field|Collection bedtype
     * @property Show\Field|Collection room_class
     * @property Show\Field|Collection max_adults
     * @property Show\Field|Collection max_children
     * @property Show\Field|Collection max_occupancy
     * @property Show\Field|Collection rollaway_count
     * @property Show\Field|Collection is_online
     * @property Show\Field|Collection selling_order
     * @property Show\Field|Collection updatetime
     * @property Show\Field|Collection rma_code
     * @property Show\Field|Collection booker_text_lang
     * @property Show\Field|Collection room_name_en_new
     * @property Show\Field|Collection booker_text_en_new
     * @property Show\Field|Collection app_name
     * @property Show\Field|Collection admin_id
     * @property Show\Field|Collection attr_name
     * @property Show\Field|Collection attr_type
     * @property Show\Field|Collection attr_value
     * @property Show\Field|Collection collect
     * @property Show\Field|Collection first_name
     * @property Show\Field|Collection last_name
     * @property Show\Field|Collection email_verified_at
     * @property Show\Field|Collection city
     * @property Show\Field|Collection country
     * @property Show\Field|Collection is_message
     * @property Show\Field|Collection is_auth
     * @property Show\Field|Collection profile_id
     * @property Show\Field|Collection membership_card_no
     * @property Show\Field|Collection wellness_id
     *
     * @method Show\Field|Collection id(string $label = null)
     * @method Show\Field|Collection hotal_name(string $label = null)
     * @method Show\Field|Collection activity_type(string $label = null)
     * @method Show\Field|Collection name_en(string $label = null)
     * @method Show\Field|Collection name(string $label = null)
     * @method Show\Field|Collection address(string $label = null)
     * @method Show\Field|Collection is_show(string $label = null)
     * @method Show\Field|Collection web_desc(string $label = null)
     * @method Show\Field|Collection key_words(string $label = null)
     * @method Show\Field|Collection page_title(string $label = null)
     * @method Show\Field|Collection activity_link(string $label = null)
     * @method Show\Field|Collection rate(string $label = null)
     * @method Show\Field|Collection access_code(string $label = null)
     * @method Show\Field|Collection status(string $label = null)
     * @method Show\Field|Collection line_type(string $label = null)
     * @method Show\Field|Collection url(string $label = null)
     * @method Show\Field|Collection button_text(string $label = null)
     * @method Show\Field|Collection date(string $label = null)
     * @method Show\Field|Collection img_url(string $label = null)
     * @method Show\Field|Collection img_url_f(string $label = null)
     * @method Show\Field|Collection content(string $label = null)
     * @method Show\Field|Collection order(string $label = null)
     * @method Show\Field|Collection user_id(string $label = null)
     * @method Show\Field|Collection deleted_at(string $label = null)
     * @method Show\Field|Collection created_at(string $label = null)
     * @method Show\Field|Collection updated_at(string $label = null)
     * @method Show\Field|Collection type(string $label = null)
     * @method Show\Field|Collection version(string $label = null)
     * @method Show\Field|Collection detail(string $label = null)
     * @method Show\Field|Collection is_enabled(string $label = null)
     * @method Show\Field|Collection parent_id(string $label = null)
     * @method Show\Field|Collection icon(string $label = null)
     * @method Show\Field|Collection uri(string $label = null)
     * @method Show\Field|Collection extension(string $label = null)
     * @method Show\Field|Collection permission_id(string $label = null)
     * @method Show\Field|Collection menu_id(string $label = null)
     * @method Show\Field|Collection slug(string $label = null)
     * @method Show\Field|Collection http_method(string $label = null)
     * @method Show\Field|Collection http_path(string $label = null)
     * @method Show\Field|Collection role_id(string $label = null)
     * @method Show\Field|Collection value(string $label = null)
     * @method Show\Field|Collection username(string $label = null)
     * @method Show\Field|Collection password(string $label = null)
     * @method Show\Field|Collection avatar(string $label = null)
     * @method Show\Field|Collection email(string $label = null)
     * @method Show\Field|Collection wx_openid(string $label = null)
     * @method Show\Field|Collection remember_token(string $label = null)
     * @method Show\Field|Collection order_num(string $label = null)
     * @method Show\Field|Collection code(string $label = null)
     * @method Show\Field|Collection key(string $label = null)
     * @method Show\Field|Collection expiration(string $label = null)
     * @method Show\Field|Collection owner(string $label = null)
     * @method Show\Field|Collection city_code(string $label = null)
     * @method Show\Field|Collection country_code(string $label = null)
     * @method Show\Field|Collection city_name(string $label = null)
     * @method Show\Field|Collection city_name_en(string $label = null)
     * @method Show\Field|Collection group(string $label = null)
     * @method Show\Field|Collection sort(string $label = null)
     * @method Show\Field|Collection image(string $label = null)
     * @method Show\Field|Collection country_name(string $label = null)
     * @method Show\Field|Collection country_name_en(string $label = null)
     * @method Show\Field|Collection country_name_pinyin(string $label = null)
     * @method Show\Field|Collection continent_name_en(string $label = null)
     * @method Show\Field|Collection brief_en(string $label = null)
     * @method Show\Field|Collection brief(string $label = null)
     * @method Show\Field|Collection detail_en(string $label = null)
     * @method Show\Field|Collection introducing(string $label = null)
     * @method Show\Field|Collection introducing_en(string $label = null)
     * @method Show\Field|Collection image_url(string $label = null)
     * @method Show\Field|Collection country_url(string $label = null)
     * @method Show\Field|Collection country_link(string $label = null)
     * @method Show\Field|Collection uuid(string $label = null)
     * @method Show\Field|Collection connection(string $label = null)
     * @method Show\Field|Collection queue(string $label = null)
     * @method Show\Field|Collection payload(string $label = null)
     * @method Show\Field|Collection exception(string $label = null)
     * @method Show\Field|Collection failed_at(string $label = null)
     * @method Show\Field|Collection version_no(string $label = null)
     * @method Show\Field|Collection hotelcode(string $label = null)
     * @method Show\Field|Collection hotel_type(string $label = null)
     * @method Show\Field|Collection region(string $label = null)
     * @method Show\Field|Collection hotel_name(string $label = null)
     * @method Show\Field|Collection hotel_name_en(string $label = null)
     * @method Show\Field|Collection hotel_name_pinyin(string $label = null)
     * @method Show\Field|Collection gradient_color(string $label = null)
     * @method Show\Field|Collection primary_currency(string $label = null)
     * @method Show\Field|Collection new_hotel_start_date(string $label = null)
     * @method Show\Field|Collection new_hotel_end_date(string $label = null)
     * @method Show\Field|Collection synxis_hotel_id(string $label = null)
     * @method Show\Field|Collection synxis_chain_id(string $label = null)
     * @method Show\Field|Collection sabre_tax_setup(string $label = null)
     * @method Show\Field|Collection online_status(string $label = null)
     * @method Show\Field|Collection address_en(string $label = null)
     * @method Show\Field|Collection postal_code(string $label = null)
     * @method Show\Field|Collection phone(string $label = null)
     * @method Show\Field|Collection fax(string $label = null)
     * @method Show\Field|Collection zero_rate_code(string $label = null)
     * @method Show\Field|Collection phone_country_code(string $label = null)
     * @method Show\Field|Collection phone_area_code(string $label = null)
     * @method Show\Field|Collection is_up_pay(string $label = null)
     * @method Show\Field|Collection offered_service(string $label = null)
     * @method Show\Field|Collection longitude(string $label = null)
     * @method Show\Field|Collection latitude(string $label = null)
     * @method Show\Field|Collection hotel_desc(string $label = null)
     * @method Show\Field|Collection condition_code(string $label = null)
     * @method Show\Field|Collection remark(string $label = null)
     * @method Show\Field|Collection floor_rate(string $label = null)
     * @method Show\Field|Collection highlight(string $label = null)
     * @method Show\Field|Collection award_winning_dinning(string $label = null)
     * @method Show\Field|Collection website_url(string $label = null)
     * @method Show\Field|Collection unique_quelities_en(string $label = null)
     * @method Show\Field|Collection unique_quelities(string $label = null)
     * @method Show\Field|Collection rooms_total_number(string $label = null)
     * @method Show\Field|Collection floors_number(string $label = null)
     * @method Show\Field|Collection restaurants_number(string $label = null)
     * @method Show\Field|Collection bars_number(string $label = null)
     * @method Show\Field|Collection hasPools(string $label = null)
     * @method Show\Field|Collection michelinStarredRestaurants(string $label = null)
     * @method Show\Field|Collection edit_time(string $label = null)
     * @method Show\Field|Collection trust_online(string $label = null)
     * @method Show\Field|Collection hotel_link(string $label = null)
     * @method Show\Field|Collection hotel_alias_en(string $label = null)
     * @method Show\Field|Collection crs_api(string $label = null)
     * @method Show\Field|Collection check_in_after(string $label = null)
     * @method Show\Field|Collection check_out_before(string $label = null)
     * @method Show\Field|Collection brand_code(string $label = null)
     * @method Show\Field|Collection brand_nameEn(string $label = null)
     * @method Show\Field|Collection brand_name(string $label = null)
     * @method Show\Field|Collection max_childrenage(string $label = null)
     * @method Show\Field|Collection open_date(string $label = null)
     * @method Show\Field|Collection extend(string $label = null)
     * @method Show\Field|Collection images(string $label = null)
     * @method Show\Field|Collection destinations(string $label = null)
     * @method Show\Field|Collection interests(string $label = null)
     * @method Show\Field|Collection eco_certificates(string $label = null)
     * @method Show\Field|Collection feature(string $label = null)
     * @method Show\Field|Collection new_hotel(string $label = null)
     * @method Show\Field|Collection categories(string $label = null)
     * @method Show\Field|Collection neighborhood_tag(string $label = null)
     * @method Show\Field|Collection title_en(string $label = null)
     * @method Show\Field|Collection country_code_en(string $label = null)
     * @method Show\Field|Collection city_code_en(string $label = null)
     * @method Show\Field|Collection logo(string $label = null)
     * @method Show\Field|Collection logo_svg(string $label = null)
     * @method Show\Field|Collection card_logo(string $label = null)
     * @method Show\Field|Collection location_id(string $label = null)
     * @method Show\Field|Collection hotel_code(string $label = null)
     * @method Show\Field|Collection sub_title(string $label = null)
     * @method Show\Field|Collection sub_title_en(string $label = null)
     * @method Show\Field|Collection cuisine(string $label = null)
     * @method Show\Field|Collection website(string $label = null)
     * @method Show\Field|Collection reservation_url(string $label = null)
     * @method Show\Field|Collection menu_1_url(string $label = null)
     * @method Show\Field|Collection menu_2_url(string $label = null)
     * @method Show\Field|Collection start_time(string $label = null)
     * @method Show\Field|Collection end_time(string $label = null)
     * @method Show\Field|Collection dining_id(string $label = null)
     * @method Show\Field|Collection original_id(string $label = null)
     * @method Show\Field|Collection external_id(string $label = null)
     * @method Show\Field|Collection title_cn(string $label = null)
     * @method Show\Field|Collection hotels(string $label = null)
     * @method Show\Field|Collection related_rate_codes(string $label = null)
     * @method Show\Field|Collection start_date(string $label = null)
     * @method Show\Field|Collection end_date(string $label = null)
     * @method Show\Field|Collection stay_start_date(string $label = null)
     * @method Show\Field|Collection stay_end_date(string $label = null)
     * @method Show\Field|Collection rate_code1(string $label = null)
     * @method Show\Field|Collection rate_code2(string $label = null)
     * @method Show\Field|Collection rate_code3(string $label = null)
     * @method Show\Field|Collection rate_code4(string $label = null)
     * @method Show\Field|Collection rate_checked(string $label = null)
     * @method Show\Field|Collection featured(string $label = null)
     * @method Show\Field|Collection offer_types(string $label = null)
     * @method Show\Field|Collection promo_type(string $label = null)
     * @method Show\Field|Collection promo_code(string $label = null)
     * @method Show\Field|Collection description_en(string $label = null)
     * @method Show\Field|Collection distribution(string $label = null)
     * @method Show\Field|Collection highlights(string $label = null)
     * @method Show\Field|Collection offerIncludes(string $label = null)
     * @method Show\Field|Collection offerIncludes_en(string $label = null)
     * @method Show\Field|Collection terms_conditions(string $label = null)
     * @method Show\Field|Collection terms_conditions_en(string $label = null)
     * @method Show\Field|Collection taxes(string $label = null)
     * @method Show\Field|Collection taxes_en(string $label = null)
     * @method Show\Field|Collection members_only(string $label = null)
     * @method Show\Field|Collection percentage_off(string $label = null)
     * @method Show\Field|Collection total_jobs(string $label = null)
     * @method Show\Field|Collection pending_jobs(string $label = null)
     * @method Show\Field|Collection failed_jobs(string $label = null)
     * @method Show\Field|Collection failed_job_ids(string $label = null)
     * @method Show\Field|Collection cancelled_at(string $label = null)
     * @method Show\Field|Collection finished_at(string $label = null)
     * @method Show\Field|Collection attempts(string $label = null)
     * @method Show\Field|Collection reserved_at(string $label = null)
     * @method Show\Field|Collection available_at(string $label = null)
     * @method Show\Field|Collection msg(string $label = null)
     * @method Show\Field|Collection author(string $label = null)
     * @method Show\Field|Collection token(string $label = null)
     * @method Show\Field|Collection tokenable_type(string $label = null)
     * @method Show\Field|Collection tokenable_id(string $label = null)
     * @method Show\Field|Collection abilities(string $label = null)
     * @method Show\Field|Collection last_used_at(string $label = null)
     * @method Show\Field|Collection expires_at(string $label = null)
     * @method Show\Field|Collection brandCode(string $label = null)
     * @method Show\Field|Collection roomcode(string $label = null)
     * @method Show\Field|Collection room_id(string $label = null)
     * @method Show\Field|Collection synxis_room_id(string $label = null)
     * @method Show\Field|Collection size(string $label = null)
     * @method Show\Field|Collection room_name(string $label = null)
     * @method Show\Field|Collection room_name_en(string $label = null)
     * @method Show\Field|Collection room_desc(string $label = null)
     * @method Show\Field|Collection room_desc_en(string $label = null)
     * @method Show\Field|Collection booker_text(string $label = null)
     * @method Show\Field|Collection booker_text_en(string $label = null)
     * @method Show\Field|Collection free_wifi(string $label = null)
     * @method Show\Field|Collection bedtype(string $label = null)
     * @method Show\Field|Collection room_class(string $label = null)
     * @method Show\Field|Collection max_adults(string $label = null)
     * @method Show\Field|Collection max_children(string $label = null)
     * @method Show\Field|Collection max_occupancy(string $label = null)
     * @method Show\Field|Collection rollaway_count(string $label = null)
     * @method Show\Field|Collection is_online(string $label = null)
     * @method Show\Field|Collection selling_order(string $label = null)
     * @method Show\Field|Collection updatetime(string $label = null)
     * @method Show\Field|Collection rma_code(string $label = null)
     * @method Show\Field|Collection booker_text_lang(string $label = null)
     * @method Show\Field|Collection room_name_en_new(string $label = null)
     * @method Show\Field|Collection booker_text_en_new(string $label = null)
     * @method Show\Field|Collection app_name(string $label = null)
     * @method Show\Field|Collection admin_id(string $label = null)
     * @method Show\Field|Collection attr_name(string $label = null)
     * @method Show\Field|Collection attr_type(string $label = null)
     * @method Show\Field|Collection attr_value(string $label = null)
     * @method Show\Field|Collection collect(string $label = null)
     * @method Show\Field|Collection first_name(string $label = null)
     * @method Show\Field|Collection last_name(string $label = null)
     * @method Show\Field|Collection email_verified_at(string $label = null)
     * @method Show\Field|Collection city(string $label = null)
     * @method Show\Field|Collection country(string $label = null)
     * @method Show\Field|Collection is_message(string $label = null)
     * @method Show\Field|Collection is_auth(string $label = null)
     * @method Show\Field|Collection profile_id(string $label = null)
     * @method Show\Field|Collection membership_card_no(string $label = null)
     * @method Show\Field|Collection wellness_id(string $label = null)
     */
    class Show {}

    /**
     * @method \Dcat\Admin\Form\Extend\Distpicker\Form\Distpicker distpicker(...$params)
     * @method \Dcat\Admin\Form\Extend\Diyforms\Form\DiyForm diyForm(...$params)
     * @method \Dcat\Admin\Form\Extend\FormMedia\Form\Field\Iconimg iconimg(...$params)
     * @method \Dcat\Admin\Form\Extend\FormMedia\Form\Field\Photo photo(...$params)
     * @method \Dcat\Admin\Form\Extend\FormMedia\Form\Field\Photos photos(...$params)
     * @method \Dcat\Admin\Form\Extend\FormMedia\Form\Field\Video video(...$params)
     * @method \Dcat\Admin\Form\Extend\FormMedia\Form\Field\Audio audio(...$params)
     * @method \Dcat\Admin\Form\Extend\FormMedia\Form\Field\Files files(...$params)
     */
    class Form {}

}

namespace Dcat\Admin\Grid {
    /**
     * @method $this distpicker(...$params)
     */
    class Column {}

    /**
     * @method \Dcat\Admin\Form\Extend\Distpicker\Filter\DistpickerFilter distpicker(...$params)
     */
    class Filter {}
}

namespace Dcat\Admin\Show {
    /**
     * @method $this diyForm(...$params)
     */
    class Field {}
}
