# GXP 配置化认证机制

本文档详细说明了GXP服务的配置化认证机制，实现了基于配置文件的自动认证和透明token管理。

## 🎯 设计理念

### 核心原则
- **配置驱动**: 认证凭据统一在配置文件中管理
- **零参数传递**: 业务方法无需传递username和password
- **自动初始化**: 服务启动时自动从配置文件读取凭据
- **透明管理**: 完全透明的token获取、缓存和刷新

### 架构优势
- **简化API**: 业务方法签名更加简洁
- **统一管理**: 所有GXP凭据集中配置
- **安全性**: 避免在代码中硬编码或传递敏感信息
- **可维护性**: 配置变更无需修改代码

## ⚙️ 配置设置

### 环境变量配置
在 `.env` 文件中添加：
```env
# GXP API配置
GHA_API_V1=https://gxp.stage.ghaloyalty.com/api/v1/
GHA_APP_KEY_CHINA=your_china_app_key_here
GHA_APP_KEY_ROTANA=your_rotana_app_key_here

# GXP用户凭据
GHA_GXP_USERNAME=your_gxp_username
GHA_GXP_PASSWORD=your_gxp_password
```

### 配置文件结构
`config/gha.php`:
```php
return [
    'api_v1' => env('GHA_API_V1', 'https://gxp.stage.ghaloyalty.com/api/v1/'),
    'app_key_china' => env('GHA_APP_KEY_CHINA', ''),
    'app_key_rotana' => env('GHA_APP_KEY_ROTANA', ''),
    
    // GXP用户凭据配置
    'gxp_username' => env('GHA_GXP_USERNAME', ''),
    'gxp_password' => env('GHA_GXP_PASSWORD', ''),
];
```

## 🏗️ 服务实现

### 自动初始化机制
```php
class GxpService
{
    protected bool $initialized = false;

    public function __construct(GxpHttpService $httpService)
    {
        $this->httpService = $httpService;
        $this->initializeFromConfig(); // 自动初始化
    }

    protected function initializeFromConfig(): void
    {
        $this->username = config('gha.gxp_username');
        $this->password = config('gha.gxp_password');
        
        if ($this->username && $this->password) {
            $this->initialized = true;
            Log::info('GXP服务已从配置文件初始化', ['username' => $this->username]);
        }
    }
}
```

### 透明Token管理
```php
public function getCurrencyExchangeRate(string $currency, string $startDate, int $size = 10): array
{
    // 自动获取token，无需手动传递凭据
    $token = $this->getAuthToken();
    
    // 调用API...
}

protected function getAuthToken(): string
{
    $this->ensureCredentialsSet(); // 确保已初始化
    return $this->getOrRefreshToken($this->username, $this->password);
}
```

## 📋 API使用指南

### 控制器层使用
```php
class GxpController extends BaseController
{
    public function getCurrencyConversionRate(Request $request)
    {
        $this->validate($request->all(), [
            'currency' => 'required|string',
            'start_date' => 'required|date',
            'size' => 'sometimes|integer|min:1|max:100'
            // 不再需要username和password参数
        ]);

        // 验证用户已登录我们的系统
        $user = Auth::user();
        if (!$user) {
            return errorResponse('未授权访问', 401);
        }

        // 直接调用服务，凭据自动从配置读取
        $result = $this->gxpService->getCurrencyExchangeRate(
            $request->currency,
            $request->start_date,
            $request->size ?? 10
        );

        return successResponse($result, '获取汇率成功');
    }
}
```

### 服务层使用
```php
class BookingService
{
    public function syncReservations()
    {
        $gxpService = app(GxpService::class);
        
        // 无需设置凭据，自动从配置读取
        $bookings = $gxpService->getUserBookings([
            'status' => ['CURRENT', 'UPCOMING'],
            'size' => 50
        ]);
        
        foreach ($bookings['bookings'] as $booking) {
            if ($this->needsUpdate($booking)) {
                // 同样无需传递凭据
                $result = $gxpService->submitReservation([
                    'itinerary_number' => $booking->itineraryNumber,
                    'status' => 'MODIFY',
                    // ... 其他数据
                ]);
            }
        }
    }
}
```

### 命令行使用
```php
class SyncGxpDataCommand extends Command
{
    public function handle(GxpService $gxpService)
    {
        $this->info('开始同步GXP数据...');
        
        try {
            // 检查服务是否已初始化
            if (!$gxpService->isInitialized()) {
                $this->error('GXP服务未初始化，请检查配置文件');
                return 1;
            }
            
            // 直接调用，无需传递凭据
            $rates = $gxpService->getCurrencyExchangeRate('USD', now()->format('Y-m-d'));
            $this->processRates($rates);
            
            $bookings = $gxpService->getUserBookings();
            $this->processBookings($bookings);
            
            $this->info('同步完成');
            
        } catch (Exception $e) {
            $this->error('同步失败: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
```

## 🔧 高级功能

### 运行时凭据覆盖
如果需要在运行时使用不同的凭据：
```php
$gxpService = app(GxpService::class);

// 临时使用不同的凭据
$result = $gxpService
    ->setCredentials('different_user', 'different_pass')
    ->getCurrencyExchangeRate('USD', '2025-01-01');

// 后续调用仍使用配置文件中的凭据
$bookings = $gxpService->getUserBookings();
```

### 服务状态检查
```php
$gxpService = app(GxpService::class);

if ($gxpService->isInitialized()) {
    echo "服务已初始化，使用用户: " . config('gha.gxp_username');
} else {
    echo "服务未初始化，请检查配置";
}
```

### 缓存管理
```php
$gxpService = app(GxpService::class);

// 查看当前缓存信息
$info = $gxpService->getCurrentTokenCacheInfo();
if ($info) {
    echo "Token过期时间: " . date('Y-m-d H:i:s', $info['expires_at']);
}

// 清除当前缓存
$gxpService->clearCurrentTokenCache();
```

## 🛠️ 命令行工具

### 基本使用
```bash
# 查看token缓存信息（使用配置文件凭据）
php artisan gxp:token info

# 清除token缓存
php artisan gxp:token clear

# 刷新token
php artisan gxp:token refresh
```

### 使用自定义凭据
```bash
# 使用指定凭据查看缓存信息
php artisan gxp:token info --username=custom_user --password=custom_pass

# 清除指定用户的缓存
php artisan gxp:token clear --username=custom_user --password=custom_pass
```

## 🧪 测试支持

### 单元测试
```php
public function test_config_based_authentication()
{
    // 设置测试配置
    config(['gha.gxp_username' => 'test_user']);
    config(['gha.gxp_password' => 'test_pass']);
    
    $mockService = Mockery::mock(GxpHttpService::class);
    $gxpService = new GxpService($mockService);
    
    // 验证自动初始化
    $this->assertTrue($gxpService->isInitialized());
    
    // 模拟API调用
    $mockService->shouldReceive('post')->once()
        ->andReturn(['code' => 200, 'data' => ['token' => 'test_token']]);
    
    $mockService->shouldReceive('get')->once()
        ->andReturn(['code' => 200, 'data' => ['rates' => []]]);
    
    // 测试无参数调用
    $result = $gxpService->getCurrencyExchangeRate('USD', '2025-01-01');
    
    $this->assertIsArray($result);
}
```

### 集成测试
```php
public function test_real_api_calls()
{
    // 确保测试环境有正确的配置
    $this->assertNotEmpty(config('gha.gxp_username'));
    $this->assertNotEmpty(config('gha.gxp_password'));
    
    $gxpService = app(GxpService::class);
    
    $this->assertTrue($gxpService->isInitialized());
    
    $result = $gxpService->getCurrencyExchangeRate('USD', now()->format('Y-m-d'));
    
    $this->assertArrayHasKey('rates', $result);
}
```

## 🔒 安全考虑

### 配置文件安全
- 确保 `.env` 文件不被提交到版本控制
- 在生产环境中使用环境变量而非配置文件
- 定期轮换GXP凭据

### 日志安全
```php
// 服务会自动记录初始化日志，但不记录密码
Log::info('GXP服务已从配置文件初始化', ['username' => $this->username]);
// 密码不会被记录到日志中
```

### 错误处理
```php
protected function ensureCredentialsSet(): void
{
    if (!$this->isInitialized()) {
        throw new Exception('GXP服务未初始化，请检查配置文件中的gha.gxp_username和gha.gxp_password配置');
    }
}
```

## 📈 性能优化

### 启动性能
- 服务在构造时自动初始化，无额外开销
- 配置读取只在服务实例化时进行一次
- 缓存机制确保token重用

### 内存使用
- 凭据只在服务实例中存储一份
- 不会在每次方法调用时重复读取配置
- 自动垃圾回收清理过期缓存

## 🎉 迁移指南

### 从手动传参迁移
```php
// 旧方式
$result = $gxpService->getCurrencyExchangeRate($username, $password, 'USD', '2025-01-01');

// 新方式
$result = $gxpService->getCurrencyExchangeRate('USD', '2025-01-01');
```

### 控制器更新
```php
// 移除参数验证中的username和password
$this->validate($request->all(), [
    'currency' => 'required|string',
    'start_date' => 'required|date',
    // 移除: 'username' => 'required|string',
    // 移除: 'password' => 'required|string'
]);

// 简化服务调用
$result = $this->gxpService->getCurrencyExchangeRate(
    $request->currency,
    $request->start_date
    // 移除: $request->username,
    // 移除: $request->password
);
```

## 📋 最佳实践

1. **环境配置**: 在不同环境使用不同的GXP凭据
2. **配置验证**: 在应用启动时验证必需的配置项
3. **错误监控**: 监控初始化失败和认证错误
4. **定期维护**: 定期更新和轮换凭据
5. **文档同步**: 保持配置文档与实际配置同步

这种配置化认证机制大大简化了GXP服务的使用，提高了代码的可维护性和安全性，同时保持了高性能和灵活性。
