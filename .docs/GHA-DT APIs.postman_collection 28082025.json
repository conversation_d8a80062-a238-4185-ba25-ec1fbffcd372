{"info": {"_postman_id": "b90a82d5-ccab-4f6d-8193-3569ec574b6c", "name": "GHA-DT APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "33838933", "_collection_link": "https://gha-e-com.postman.co/workspace/GHA-Microservices~4601d23f-2ec1-464a-8fe5-92376c34c826/collection/33838933-b90a82d5-ccab-4f6d-8193-3569ec574b6c?action=share&source=collection_link&creator=33838933"}, "item": [{"name": "china", "item": [{"name": "Brand API create member", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": {{email_address}},\r\n    \"firstName\": {{first_name}},\r\n    \"lastName\": {{last_name}},\r\n    \"password\": {{password}},\r\n    \"language\": \"E\",\r\n    \"enrollmentCode\": {{enrollment_code}},\r\n    \"ghaMarketingYn\": {{gha_marketing_YN}} // Boolean value : true/false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://escp.stage.ghaloyalty.com/brand-api/v3/createMember?token={{brand_token}}", "protocol": "https", "host": ["escp", "stage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["brand-api", "v3", "createMember"], "query": [{"key": "token", "value": "{{brand_token}}"}]}}, "response": []}, {"name": "Brand API Login", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": {{username}},\r\n    \"password\": {{password}}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://escp.stage.ghaloyalty.com/brand-api/v3/memberLogin?token={{brand_token}}&include_datamart_memberships=ACTIVE&language=en", "protocol": "https", "host": ["escp", "stage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["brand-api", "v3", "<PERSON><PERSON><PERSON><PERSON>"], "query": [{"key": "token", "value": "{{brand_token}}"}, {"key": "include_datamart_memberships", "value": "ACTIVE"}, {"key": "language", "value": "en"}]}}, "response": []}, {"name": "Reserve repo posting", "request": {"method": "POST", "header": [{"key": "apiauthorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "app-key", "value": "{{app-key-china}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"profile_id\": {{profile_id}}, // profile_id only required for PARTNER type user\r\n    \"itinerary_number\": {{itinerary_number}},\r\n    \"status\": {{reservation_status}}, //Required status: CREATE, MODIFY, CANCEL\r\n    \"source\": \"GHA\",\r\n    \"chain_id\": {{chain_id}},\r\n    \"hotel_id\": {{hotel_id}},\r\n    \"reservation_details\": [\r\n        {\r\n            \"confirmation_number\": {{confirmation_number}},\r\n            \"member_rate_yn\": {{member_rate}}     // Boolean value : true/false\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://gxp.stage.ghaloyalty.com/api/v1/reservations-repo", "protocol": "https", "host": ["gxp", "stage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["api", "v1", "reservations-repo"]}}, "response": []}, {"name": "My Booking", "request": {"method": "GET", "header": [{"key": "apiauthorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "app-key", "value": "{{app-key-china}}", "type": "text"}], "url": {"raw": "https://gxp.stage.ghaloyalty.com/api/v1/reservations-repo?page=0&size=20&status=CURRENT&status=UPCOMING&status=PAST&status=CANCELED&sort_by=ArrivalDate&sort_direction=desc", "protocol": "https", "host": ["gxp", "stage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["api", "v1", "reservations-repo"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "status", "value": "CURRENT"}, {"key": "status", "value": "UPCOMING"}, {"key": "status", "value": "PAST"}, {"key": "status", "value": "CANCELED"}, {"key": "sort_by", "value": "ArrivalDate"}, {"key": "sort_direction", "value": "desc"}, {"key": "profile_id", "value": "{{profileId}}", "description": "Required for PARTNER user type", "disabled": true}]}}, "response": []}, {"name": "Guest Token", "request": {"method": "POST", "header": [{"key": "app-key", "value": "{{app-key-china}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"GUEST\",\n  \"username\": \"{{username}}\",\n  \"password\": \"{{password}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://gxp.stage.ghaloyalty.com/api/v1/auth/token", "protocol": "https", "host": ["gxp", "stage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["api", "v1", "auth", "token"]}}, "response": []}, {"name": "Sabre Token", "request": {"method": "POST", "header": [{"key": "X-API-KEY", "value": "{{x-api-key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"userName\": {{username}},\n    \"password\": {{password}}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://oscp.stage.ghaloyalty.com/api/v3/auth/token", "protocol": "https", "host": ["oscp", "stage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["api", "v3", "auth", "token"]}}, "response": []}, {"name": "Forgot Membership", "request": {"method": "POST", "header": [{"key": "authorization", "value": "Basic Z2hhOnVFNlU4d253aExzVTVHa1k=", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": {{email}},\n    \"lastName\": {{lastname}}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://oscp.stage.ghaloyalty.com/api/v2/member/restore/membership/number", "protocol": "https", "host": ["oscp", "stage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["api", "v2", "member", "restore", "membership", "number"]}}, "response": []}, {"name": "Get Currency Conversion Rate", "request": {"method": "GET", "header": [{"key": "apiauthorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "app-key", "value": "{{app-key-rotana}}", "type": "text"}], "url": {"raw": "https://gxp.stage.ghaloyalty.com/api/v1/ddollars/exchange-rate?currency={{currency}}&start_date=2025-07-10&size={{size}}", "protocol": "https", "host": ["gxp", "stage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com"], "path": ["api", "v1", "ddollars", "exchange-rate"], "query": [{"key": "currency", "value": "{{currency}}"}, {"key": "start_date", "value": "2025-07-10"}, {"key": "size", "value": "{{size}}"}]}}, "response": []}]}]}