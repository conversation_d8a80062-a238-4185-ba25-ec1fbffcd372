.home-banner-wrap {
  .swiper {
    @apply h-[320px] w-full md:h-[600px];
    .swiper-item {
      @apply h-full bg-center bg-cover bg-no-repeat flex items-center justify-center;
      .item-content {
        @apply text-white flex flex-col items-center text-center;
        h2 {
          @apply text-3xl;
        }
        h4 {
          @apply text-xl mt-2;
        }
        a {
          @apply bg-[#da9f59] rounded-full px-8 py-2.5 text-sm mt-2;
        }
      }
    }
  }
}

.home-filter-wrap {
  @apply -mt-16 relative z-[1996];
  .inner {
    box-shadow: 0px 0px 9px 2px rgba(0, 0, 0, 0.06) ;
    @apply rounded-[14px] overflow-hidden border-b;
  }
  .tab-wrap {
    @apply hidden lg:flex flex-row border-b lg:border-b-0 lg:bg-black/30 overflow-hidden;
    .tab-list {
      @apply flex w-full lg:w-auto lg:inline-flex items-center rounded-t-[14px] overflow-hidden ;
      &.active-1 {
        .item:nth-child(1) {
          p {
            @apply rounded-t-[14px] bg-white text-black font-medium;
          }
          
          &::before {
            @apply content-[""] absolute w-[28px] h-1/2 bg-[#094f7f] right-0 z-0;
          }
          &::after {
            @apply content-[""] absolute w-[28px] h-1/2 bg-[#fff] -right-[14px] bottom-0 z-0;
          }
        }
        .item:nth-child(2) p {
          @apply rounded-bl-[14px];
        }
      }
      &.active-2 {
        .item:nth-child(1) {
          p {
            @apply rounded-br-[14px];
          }
          &::after {
            @apply content-[""] absolute w-[28px] h-1/2 bg-[#2d0f4f] -right-[14px] top-0 z-0;
          }
        }
        .item:nth-child(3) {
          p {
            @apply rounded-bl-[14px];
          }
          &::before {
            @apply content-[""] absolute w-[28px] h-1/2 bg-[#79b4d1] -left-[14px] top-0 z-0;
          }
        }
        .item:nth-child(2) {
          p {
            @apply rounded-t-[14px] bg-white text-black font-medium;
          }
          &::before {
            @apply content-[""] absolute w-[28px] h-1/2 bg-[#fff] -left-[14px] bottom-0 z-0;
          }
          &::after {
            @apply content-[""] absolute w-[28px] h-1/2 bg-[#fff] -right-[14px] bottom-0 z-0;
          }
        }
        // .item:nth-child(2) p {
        //   @apply rounded-bl-[14px];
        // }
      }
      &.active-3 {
        .item:nth-child(3) {
          p {
            @apply rounded-tl-[14px] bg-white text-black font-medium;
          }
          
          &::before {
            @apply content-[""] absolute w-[28px] h-1/2 bg-[#094f7f] -left-[14px] z-0;
          }
          &::after {
            @apply content-[""] absolute w-[28px] h-1/2 bg-[#fff] -left-[14px] bottom-0 z-0;
          }
        }
        .item:nth-child(2) p {
          @apply rounded-br-[14px];
        }
      }
      .item {
        @apply text-base leading-[60px] lg:leading-[50px] lg:w-40 flex-1 lg:flex-auto text-white text-center cursor-pointer relative;
        &:nth-child(n + 2) {
          @apply hidden;
        }
        p {
          @apply relative z-10;
        }
        &:nth-child(1) p {
          @apply bg-[#2d0f4f];
        }
        &:nth-child(2) p {
          @apply bg-[#094f7f];
        }
        &:nth-child(3) p {
          @apply bg-[#79b4d1];
        }
      }
    }
    .tip {
      @apply hidden lg:block text-white text-xs flex-1 text-right relative ;
      p {
        @apply absolute bottom-3 right-0 px-4;
      }
    }
  }
  .filter-bar-wrap {
    @apply bg-white rounded-b-lg flex flex-row flex-wrap;
    &.gha-fixed {
      @apply lg:fixed lg:top-[90px] lg:left-0 lg:right-0 lg:z-10 lg:border-b lg:rounded-none;
    }
    .filter-item {
      @apply flex flex-row items-center px-4 md:border-r text-base h-[60px] border-b lg:border-b-0 lg:h-[78px];
      i {
        @apply text-3xl text-[#300b5c];
      }
      .ant-input, .ant-picker-range {
        @apply px-2 text-base;
      }
      .ant-picker-input input {
        @apply text-base;
      }
      &.date {
        @apply w-full md:w-1/2 lg:w-[300px];
      }
      &.keyword {
        @apply w-full md:w-1/2 lg:w-[230px];
      }
      &.reward, &.people {
        @apply w-1/2 lg:flex-1;
      }
      &.people {
        @apply border-r;
        a {
          @apply px-2;
        }
      }
    }
    .submit-wrap {
      @apply h-[60px] w-full lg:w-auto lg:h-[78px] flex-shrink-0 lg:leading-none lg:px-10 bg-[#300b5c]  text-white cursor-pointer flex items-center justify-center;
    }
  }
}

.home-section-wrap {
  @apply py-8 lg:py-20;
  .title-wrap {
    @apply text-center;
    h1 {
      @apply text-4xl font-bold text-[#300b5c];
    }
    h2 {
      @apply text-2xl mt-5;
    }
    h5 {
      @apply text-sm mt-6;
    }
  }
  &.compact {
    .title-wrap {
      h1 {
        @apply font-normal;
      }
      h2 {
        @apply mt-4;
      }
      h5 {
        @apply mt-5;
      }
    }
  }
  .desc-list-wrap {
    @apply mt-10 flex flex-col md:flex-row md:-mx-2.5;
    &.explore {
      .desc-item .cover {
        @apply rounded-b-none;
      }
    }
    .desc-item {
      @apply flex-1 md:mx-2.5 flex flex-col;
      .cover {
        @apply bg-center bg-cover bg-no-repeat pb-[64.5%] rounded-2xl;
        background-image: url(https://storage.ghadiscovery.com/cdn-cgi/image/width=1920,f=auto,g=auto,fit=scale-down/img/images/9/0/8/3/1523809-1-eng-GB/c068302fd46e-D-Promotions-Australia-Maldives.jpg);
      }
      .extra-info {
        @apply bg-[#f5f5f5] px-4 py-4 text-[#999];
        h3 {
          @apply text-lg text-black;
        }
        h5 {
          @apply text-xs mt-2 flex flex-row items-center;
        }
        p {
          @apply text-xs mt-2;
          a {
            @apply text-[#300b5c] underline;
          }
        }
      }
      .info {
        @apply text-center px-4 flex flex-col justify-center mt-4 pb-16 flex-1;
        background-image: url(../images/index/border-b.png);
        background-position: left bottom;
        background-size: 100% auto;
        background-repeat: no-repeat;
        h3 {
          @apply text-xl flex-1;
        }
        p {
          @apply text-sm mt-2 text-[#999];
        }
        a {
          @apply text-base px-6 py-1.5 rounded-full text-white inline-block mt-4;
        }
      }
      &:nth-child(1) {
        .info a {
          @apply bg-[#300b5c];
          box-shadow: 0px 0px 9px 4px rgba(#300b5c, 0.36);
        }
      }
      &:nth-child(2) {
        .info a {
          @apply bg-[#da9f59];
          box-shadow: 0px 0px 9px 4px rgba(#da9f59, 0.36);
        }
      }
      &:nth-child(3) {
        .info a {
          @apply bg-[#8bbcd9];
          box-shadow: 0px 0px 9px 4px rgba(#8bbcd9, 0.36);
        }
      }
    }
  }
}

.account-section-wrap {
  @apply py-8 lg:py-20 relative bg-[#f1f1f1]/70;
  .gha-swiper-button {
    &.gha-swiper-button-prev {
      @apply left-16 xl:left-24;
    }
    &.gha-swiper-button-next {
      @apply right-16 xl:right-24;
    }
  }
  
  .swiper {
    @apply h-[60vw] md:h-[420px] mt-6 lg:mt-20;
    .swiper-item {
      @apply absolute bottom-0 left-0 right-0 overflow-hidden bg-no-repeat bg-center bg-cover;
      p {
        @apply absolute bottom-0 left-0 right-0 px-4 py-2 text-white text-sm;
        &::after {
          @apply content-[""] absolute bottom-0 left-0 right-0 -top-6 bg-gradient-to-b from-[#000]/0 to-black/20;
        }
      }
    }
    &.swiper-lg {
      .swiper-wrapper {
        .swiper-slide { 
          @apply w-[20%];
          .swiper-item {
            @apply h-[190px] rounded-xl;
          }
        }
        .swiper-slide-active {
          @apply w-[45%];
          .swiper-item {
            @apply h-full rounded-2xl;
            p {
              @apply hidden;
            }
          }
        }
      }
    }

    &.swiper-m {
      @apply mb-6;
      .swiper-wrapper {
        .swiper-slide { 
          // @apply w-4/5;
          .swiper-item {
            @apply h-full rounded-lg;
          }
        }
      }
    }
    
  }
  
  .active-info-wrap {
    @apply absolute left-32 xl:left-44 bottom-[220px] w-[24vw] z-10;
    h3 {
      @apply text-xl;
    }
    h5 {
      @apply text-sm text-[#999] mt-2;
      &::after {
        @apply content-[""] block w-16 h-[2px] bg-[#999]/50 my-2;
      }
    }
    p {
      @apply text-sm;
    }
    a {
      @apply text-sm px-4 py-1.5 rounded-full text-white inline-block mt-4 bg-[#300b5c];
      box-shadow: 0px 0px 9px 2px rgba(#300b5c, 0.36);
    }
  }
}

.home-link-wrap {
  @apply w-full bg-cover bg-center bg-no-repeat py-16 md:py-0;
  background-image: url(../images/index/link-bg.png);
  .content {
    @apply relative w-full md:h-[810px];
  }
  .text-wrap {
    @apply text-center md:text-left md:absolute md:top-48 md:left-0 text-white;
    h2 {
      @apply text-3xl leading-relaxed;
    }
    p {
      @apply text-base leading-loose mt-4;
    }
  }
  .code-wrap {
    @apply mt-12 md:mt-0 md:absolute md:bottom-48 md:right-0 text-white flex flex-row;
    .item {
      @apply flex flex-col items-center;
      .code {
        @apply w-28 h-28 rounded-full bg-white bg-no-repeat bg-center bg-cover mb-4;
      }
      p {
        @apply text-white text-sm leading-relaxed;
      }
      &:nth-child(2) {
        @apply ml-8;
        .code {
          @apply rounded-2xl;
        }
      }
    }
  }
}

.brands-wrap {
  @apply py-8 lg:py-20;
  .brands-list {
    @apply mt-8 px-8;
  }
}

.ant-picker-panels {
  @apply flex-col md:flex-row;
}


.gha-swiper-button {
  @apply absolute bottom-[220px] w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center cursor-pointer z-10;
  @apply shadow-[0px_7px_9px_0px_rgba(48,11,92,0.17)];
  &.swiper-button-disabled {
    @apply bg-[#300b5c]/60 shadow-none;
  }
  &:hover:not(.swiper-button-disabled) {
    @apply opacity-80;
    // @apply bg-[#300b5c];
    // i {
    //   @apply text-white;
    // }
  }
  i {
    @apply text-2xl;
  }
  &.gha-swiper-button-prev {
    @apply left-24;
  }
  &.gha-swiper-button-next {
    @apply right-24;
  }
}

.people-select-popover-root {
  @apply mt-24;
  .people-select-popover {
    @apply w-[calc(100vw-88px)] md:w-[500px];
    .select-item {
      @apply border-b py-4;
      
      h3 {
        @apply font-semibold font-16 mb-2 relative;
        .del-btn {
          @apply absolute right-0 top-1/2 -translate-y-1/2 px-1 cursor-pointer;
          i {
            @apply text-red-600 text-xl;
          }
        }
      }
      .item-list {
        @apply flex flex-row flex-wrap md:-mx-4;
        .people-item {
          @apply flex flex-row items-center justify-between w-full md:w-1/2 md:px-4 my-2;
          &.age {
            @apply w-full items-start md:items-center;
            .ant-select {
              @apply mt-2 first:mt-0 md:mt-0 md:px-2;
            }
            p {
              @apply pt-2 md:pt-0;
            }
          }
          p {
            @apply mr-2 font-14;
          }
          .plus {
            @apply w-10 h-10 rounded-full border flex items-center justify-center cursor-pointer !leading-none font-20;
            &.disable {
              @apply opacity-60 cursor-not-allowed;
            }
          }
          .num {
            @apply mx-4 font-16;
          }
        }
      }
    }

    .add-btn {
      @apply px-8 py-2 rounded-full bg-primary text-white cursor-pointer;
    }
  }
}


.back-top-nav-wrap {
  @apply py-8 hidden sticky bottom-0 right-6 ml-auto w-14 z-20;
  &.active {
    @apply flex;
  }
  .item {
    @apply cursor-pointer w-14 h-14 shadow-md shadow-[#4D45A8]/30 rounded-full bg-white flex items-center justify-center;
    i {
      @apply text-2xl;
    }
  }
}

