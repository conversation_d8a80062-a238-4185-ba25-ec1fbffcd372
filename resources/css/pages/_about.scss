@use "../_utils/mixins.scss" as *;

@mixin gha-swiper-widget() {
  .gha-swiper-button {
    @apply bottom-auto top-1/2 -translate-y-1/2;
  }
  .gha-swiper-button-prev {
    @apply -left-24;
  }
  .gha-swiper-button-next {
    @apply -right-24;
  }
  .swiper-pagination {
    @apply -bottom-10 !important;
    .swiper-pagination-bullet-active {
      @apply bg-primary;
    }
  }
}

.gha-swiper {
  @include gha-swiper-widget();
  .swiper-slide.transparent-slide-30 {
    @apply opacity-30;
  }
  .swiper-slide.transparent-slide-0 {
    @apply opacity-0;
  }
  .swiper-slide.transparent-slide-30, .swiper-slide.transparent-slide-0 {
    @apply transition-opacity duration-300 pointer-events-none;
  }
}

.gha-gradient-top {
  @apply bg-[linear-gradient(0deg,#e6bdc5_0%,#4e5490_50%,#15224c_100%),linear-gradient(#000000,#000000)] bg-blend-normal;
  @include about-banner-mask;
}

.about-banner {
  @apply w-full h-[320px] md:h-[560px] bg-cover bg-no-repeat bg-center;
  background-image: url(../images/about/idx-banner.png);
  @include about-banner-mask;
}
.about-gradient-top {
  @apply h-60 md:h-80 relative;
  @apply bg-[linear-gradient(0deg,#e6bdc5_0%,#4e5490_50%,#15224c_100%),linear-gradient(#000000,#000000)] bg-blend-normal;
  @include about-banner-mask;
}
.about-nav-wrap {
  // @apply -mt-10 sticky z-10 top-[90px] md:relative md:top-0;
  @apply -mt-10 z-10 sticky top-[90px];
  .sticky-element-Observer {
    @apply absolute -top-[90px];
  }
  &.sticky-active {
    @apply bg-white border-b border-[#919191]/20;
    .nav-list {
      @apply shadow-none;
    }
  }
  .g-main-content-sm {
    @apply px-0 md:px-8 xl:px-0;
  }
  .nav-list {
    @apply bg-white rounded-t-xl md:rounded-xl flex flex-row shadow-lg md:shadow-sm px-2 md:px-0;
  }
  .nav-item {
    @apply flex-1 text-center relative font-medium font-14 lg:font-16;
    &::after {
      @apply hidden md:block content-[''] absolute w-0.5 h-3 bg-[#e6e6e6] top-1/2 -translate-y-1/2 right-0;
    }
    &:last-child::after {
      @apply content-none;
    }
    > div {
      @apply h-14 md:h-20 inline-flex items-center justify-center;
    }
    &.active {
      > div {
        @apply border-b-2 md:border-b-4 border-primary;
      }
    }
  }
}

.about-mask {
  // @apply relative;
  // &::before {
  //   @apply content-[''] absolute hidden md:block w-full left-0 right-0 -top-10 bottom-20;
  //   @apply bg-gradient-to-b from-black/10 to-transparent;
  //   // @apply bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-200 via-blue-500 to-blue-200;
  // }
}

.gha-divider {
  @apply w-full flex flex-row items-center;
  &::before, &::after {
    @apply content-[''] relative block h-0 border-t border-t-[#0c0509]/10 top-1/2 -translate-y-1/2 flex-1;
  }
  span {
    @apply relative px-10;
  }
}

.brands-swiper {
  @apply bg-gradient-to-b from-transparent to-[rgba(0,0,0,0.02)];
  .brand-group {
    @apply flex flex-row flex-wrap;
    > div {
      @apply w-1/4 md:w-1/7 relative before:w-full before:content-[''] before:pb-[100%] before:block;
      img {
        @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
      }
      &:nth-child(odd) {
        @apply bg-transparent md:bg-[#f7f7f7];
      }
    }
    &.brand-group-m {
      @apply md:hidden;
    }
    &.brand-group-m:not(.show-more) {
      > div {
        &:nth-child(n + 13) {
          @apply hidden;
        }
      }
    }
  }
}

.oth-block {
  @apply flex items-center flex-col md:flex-row;
  .cover {
    @apply w-full md:w-[360px] lg:w-[500px] md:mr-4 lg:mr-8 bg-no-repeat bg-cover bg-center rounded-xl;
    @apply gha-bg-test;
    &::before {
      @apply content-[''] w-full block pb-[72%];
    }
  }
  .desc {
    @apply flex-1 mt-4 md:mt-0;
    h3 {
      @apply font-18 lg:font-24 text-primary mb-2;
    }
    h2 {
      @apply font-24 lg:font-28 text-primary font-semibold;
    }
    p {
      @apply font-14 lg:font-16 mt-2 md:mt-4;
    }
  }
  &.reverse {
    @apply md:flex-row-reverse;
    .cover {
      @apply md:ml-4 lg:ml-8 md:mr-0;
    }
  }
}

.brand-list-toggle {
  &:not(.show-more) {
    .brand-item {
      @apply max-lg:[&:nth-child(n+7)]:hidden;
      @apply lg:max-xl:[&:nth-child(n+13)]:hidden;
      @apply xl:[&:nth-child(n+16)]:hidden;
    }
  }
}

.news-item {
  @apply flex flex-row items-center py-4 border-b-2 border-black/5;
  .info {
    h1 {
      @apply font-bold font-18 font-Jost-SemiBold;
    }
    p {
      @apply font-14 mt-1.5 text-[#666666];
    }
    .extra {
      @apply flex flex-row items-center font-12 text-[#c0c0c0] mt-2;
      span:nth-child(1) {
        @apply mr-4;
      }
    }
  }
  .thumb {
    @apply w-36 flex-shrink-0 ml-2 md:ml-4 rounded-md bg-cover bg-center bg-no-repeat;
    &::before {
      @apply content-[''] w-full pb-[56.25%] block;
    }
  }
}

.gha-rich-content {
  @apply font-14 !leading-relaxed;
  img {
    @apply my-2 rounded-lg;
    @apply w-full h-auto !important;
  }
}

.brand-desc-tab-item {
  a.active {
    @apply border-b-4 border-primary;
  }
  
}

.brands-hotel-swiper {
  
}