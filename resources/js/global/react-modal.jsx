import { createRoot } from "react-dom/client";
import { $helper } from  "@/_utils/_index";
import LogoutModal from "./parts/logout-modal";
import GhaConfigProvider from "@/_components/GhaConfigProvider"
import { useEffect } from "react";

const rootEle = $helper.createReactRootElement("__react__root__app__")

function App() {
  return (
    <>
      <LogoutModal/>
    </>
  )
}

createRoot(rootEle).render(<GhaConfigProvider><App /></GhaConfigProvider>);