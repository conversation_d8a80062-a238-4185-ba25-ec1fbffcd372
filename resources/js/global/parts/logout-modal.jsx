import { useState, useEffect } from "react"
import { <PERSON><PERSON>, But<PERSON> } from "antd"
import { $http } from "@/_utils/_index"
import { useAntdMessage } from "@/_components/GhaConfigProvider";

export default function LogoutModal() {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const message = useAntdMessage()
  useEffect(() => {
    $(".logout-btn-trigger").on("click", function() {
      setOpen(true)
    })
  }, [])

  function onLogout() {
    setLoading(true)
    $http.authLogout().subscribe(res => {
      setLoading(false)
      if (res.status_code !== 200) {
        message.error(res.msg)
        return;
      }
      location.href = "/"
    })
  }
  return (
    <Modal 
      open={open || loading} 
      onCancel={() => setOpen(false)}
      footer={null}
      width={440}
      wrapClassName="logout-modal"
      centered
      transitionName="ant-fade"
    >
      <div className="">
        <h3 className="text-center font-16 font-medium">退出账号吗？</h3>
        <div className="flex flex-row justify-center mt-5">
          <Button onClick={() => setOpen(false)} type="primary" shape="round" className="gha-primary-btn !py-1 mx-2.5 w-32">取消</Button>
          <Button loading={loading} onClick={onLogout} shape="round" className="gha-btn !py-1 mx-2.5 w-32">确认</Button>
        </div>
      </div>
    </Modal>
    
  )
}