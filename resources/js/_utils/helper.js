const { Observable } = window.rxjs

function createReactRootElement(id, container) {
  const ele = document.createElement('div')
  ele.id = id || "__react__root__"
  const parentNode = container || document.body
  parentNode.appendChild(ele)
  return ele
}

function getGlobalSubject() {
  return __Global_Subject__
}

function showLoading() {
  $(".page-content").addClass("loading")
}
function hideLoading() {
  $(".page-content").removeClass("loading")
}

function showModal(type, config) {
  $helper.getGlobalSubject().emit("showModal", { type, config })
}
function showMessage(message, config) {
  $helper.getGlobalSubject().emit("showMessage", { type: "error", ...config, content: message })
}

function hash() {
  return Math.random().toString(36).substring(2, 9)
}

function getServerConfig(k) {
  return window.__Server_Data__[k]
}

function formatUsername(user) {
  return user.username
}

function clone(_) {
  return JSON.parse(JSON.stringify(_))
}

function loginProtect() {
  if (!getServerConfig("isLogin")) {
    window.loginRedirectUrl = location.href
    $(".login-btn-trigger:not(.logined)").trigger("click")
    return false
  }
  return true
}

function verifyCaptcha() {
  const appid = '193823154'
  return new Observable(obs => {
    try {
      var captcha = new TencentCaptcha(appid, (ret) => {
        obs.next(ret)
        obs.complete()
      }, {
        userLanguage: window.__currentLang__,
      });
      // 调用方法，显示验证码
      captcha.show();
    } catch (error) {
      console.error(error)
      var ticket = 'trerror_1001_' + appid + '_' + Math.floor(new Date().getTime() / 1000);
      obs.next({
        ret: -1,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error'
      })
      obs.complete()
    }
    
  })
  try {
    // 生成一个验证码对象
    // CaptchaAppId：登录验证码控制台，从【验证管理】页面进行查看。如果未创建过验证，请先新建验证。注意：不可使用客户端类型为小程序的CaptchaAppId，会导致数据统计错误。
    //callback：定义的回调函数
    
  } catch (error) {
    // 加载异常，调用验证码js加载错误处理函数
    loadErrorCallback();
  }
}

function isJsonString(str) {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

function isAntdGhaPopoverRootVisible() {
  const popoverEls = document.querySelectorAll(".gha-popover-root")
    let isPopoverOpen = false
    for (let i = 0; i < popoverEls.length; i++) {
      const el = popoverEls[i]
      const isVisible = !!(el && el.getBoundingClientRect().width > 0 && el.getBoundingClientRect().height > 0)
      if (isVisible) {
        isPopoverOpen = true
        break
      }
    }
    return isPopoverOpen
}

function encodeSearchHash(searchParams) {
  return Object.keys(searchParams).filter(key => {
    return Boolean(searchParams[key])
  }).map(key => {
    return `${key}=${encodeURIComponent(searchParams[key])}`
  }).join("&")
}

function decodeSearchHash(hash) {
  let searchParams = {}
  new URLSearchParams(hash).forEach((value, key) => {
    searchParams[key] = decodeURIComponent(value)
  })
  return searchParams
}

function isLaravelLocal() {
  return window.__Server_Data__.appEnv === "local1"
}

const $helper = {
  createReactRootElement, getGlobalSubject, showLoading, hideLoading, showModal,
  showMessage, hash, getServerConfig, formatUsername, clone, loginProtect,
  verifyCaptcha, isJsonString, isAntdGhaPopoverRootVisible,
  encodeSearchHash, decodeSearchHash, isLaravelLocal,
}

export default $helper