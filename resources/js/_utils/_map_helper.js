class GhaMapHelper {
  formatClusterPoint (e) {
    return e.map(function(e) {
        var t, o = e.id;
        return {
            type: "Feature",
            id: o,
            properties: {
                id: o
            },
            geometry: {
                type: "Point",
                coordinates: [+e.longitude, +e.latitude]
            }
        }
    })
  };

  getPopup(hotelData, slug = "hotels") {
    console.error(hotelData)
    const popupOptions = {
      closeButton: false, 
      maxWidth: "320px", 
      className: "gha-mapbox-hotel-popup-root",
      offset: [0, 25],
      anchor: 'top'
    }
    const hotelImages = (hotelData.images || []).map(uri => {
      return `<div class="swiper-slide"><div class="relative w-full h-full"><img width="658" height="428" src="${uri}"/></div></div>`
    }).join('')
    // const logoSvg = hotelData.brand_img || ""
    const logoSvg = "https://admin.dev.ghaloyalty.com/var/site/storage/images/0/8/3/0/90380-3-eng-GB/98b725aa0183-BR_Logo_140.png"
    const local = [hotelData.country, hotelData.city?.[0]?.name].filter(e => e).join("，")
    const popupContentHotelInfo = `
      <h5 class="brand">${hotelData.brand_name}</h5>
      <h2 class="title">${hotelData.hotel_name}</h2>
      ${local ? `<h5 class="local">${local}</h5>` : ''}
      <div class="spt-line"></div>
      <div class="price-wrap">
        <div class="protrude">
          <p>会员价低至</p>
          <p class="price-num">CNY1999</p>
        </div>
        <div class="">
          <p>会员价低至</p>
          <p class="price-num">CNY2999</p>
        </div>
      </div>
    `

    const popupContentOfferInfo = `
      <h5 class="brand">${hotelData.brand_name}</h5>
      <h2 class="title">${hotelData.hotel_name}</h2>
      <div class="spt-line"></div>
      ${local ? `<h5 class="local">${local}</h5>` : ''}
    `
    const popupContent = `
        <div class="gha-mapbox-hotel-popup hotel-popup-${hotelData.id}">
          <div class="swiper-el gha-swiper">
            <div class="absolute z-20 top-2.5 left-0">
              <img class="select-none max-w-2/5 max-h-1/2 filter invert ml-2.5" src="https://cms.ghadiscovery.com${logoSvg}" alt="" />
            </div>
            <div class="absolute z-20 top-2.5 right-2.5">
              <i data-id="${hotelData.id}" class="fav-icon cursor-pointer iconfont ${hotelData.liked ? 'icon-Heart-filled' : 'icon-Heart'} text-white p-1 text-4xl"></i>
            </div>
            <div class="swiper-pagination"></div>
            <div class="gha-swiper-button gha-swiper-button-prev"><i class="iconfont icon-a-Arrow-Left"></i></div>
            <div class="gha-swiper-button gha-swiper-button-next"><i class="iconfont icon-a-Arrow-Right"></i></div>
            <div class="swiper-container">  
              <div class="swiper-wrapper">
                ${hotelImages}
              </div>
            </div>
          </div>
          <div class="hotel-item hotel-item-large">
            <div class="info-wrap">
              ${slug === "hotels" ? popupContentHotelInfo : popupContentOfferInfo}
            </div>
          </div>
        </div>
      `

    return new mapboxgl.Popup(popupOptions).setHTML(popupContent);
  }

  initPopupSwiper(hotelData) {
    setTimeout(() => {
      new Swiper(`.hotel-popup-${hotelData.id} .swiper-el .swiper-container`, {
        pagination: {
          el: `.hotel-popup-${hotelData.id} .swiper-pagination`,
          clickable: true,
        },
        navigation: {
          nextEl: `.hotel-popup-${hotelData.id} .gha-swiper-button-next`,
          prevEl: `.hotel-popup-${hotelData.id} .gha-swiper-button-prev`,
        },
      });
    }, 200);
  }

  fitBoundsMap(map, points) {
    // const points = dataSourceRef.current.map(mark => [+mark.longitude, +mark.latitude])
    let bounds = new mapboxgl.LngLatBounds();

    // 遍历所有点，并将它们添加到 bounds 中
    points.forEach(function(point) {
        bounds.extend(point);
    });

    // 使用 fitBounds 方法调整视图
    map.fitBounds(bounds, {
        padding: 100,
        maxZoom: 12
    });
  }

  flyMarkerToCenter(map, coordinates) {
    map.flyTo({
      center: coordinates,
      offset: [0, -200],
      duration: 300,
      curve: 0
    })
  }
}
const $ghaMapHelper = new GhaMapHelper()
export default $ghaMapHelper