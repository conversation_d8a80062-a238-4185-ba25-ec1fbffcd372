import { useMemo } from "react"

export default function HotelItem({ hotel, className }) {
  const local = useMemo(() => {
    return [hotel.country, hotel.city?.[0]?.name].filter(e => e).join("，")
  }, [hotel])
  const logoUri = "https://admin.dev.ghaloyalty.com/var/site/storage/images/0/8/3/0/90380-3-eng-GB/98b725aa0183-BR_Logo_140.png" || hotel.brand_img || ""
  return (
    <div className={`hotel-item ${className}`}>
      <div className="cover-wrap">
        <div className="fav-icon"><i className="iconfont icon-Heart text-white"></i></div>
        <div className="logo-wrap">
          {/* <img src={(hotel.brand_img || "").replace("admin.dev.ghaloyalty.com", "cms.ghadiscovery.com")} alt="" /> */}
          <img src={logoUri} alt="" />
        </div>
        
      </div>
      <div className="tag-wrap" style={{backgroundColor: hotel.gradient_color}}>
        <p>{hotel.series}</p>
      </div>
      <div className="info-wrap">
        <div className="brand">{hotel.brand_name}</div>
        <h2 className="title">{hotel.hotel_name}</h2>
        {local && <h5 className="local">{local}</h5>}
        <div className="spt-line"></div>
        <div className="flex-1"></div>
        <div className="price-wrap">
          <div className="protrude">
            <p>会员价低至</p>
            <p className="price-num">CNY1999</p>
          </div>
          <div className="">
            <p>会员价低至</p>
            <p className="price-num">CNY2999</p>
          </div>
        </div>
        <div className="action-wrap">
          <a href="" className="gha-primary-btn">立即预订</a>
          <a href="" className="gha-btn">酒店详情</a>
        </div>
      </div>
    </div>
  )
}