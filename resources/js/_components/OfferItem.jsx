export default function OfferItem({ offer, className }) {
  return (
    <div className={`hotel-item ${className}`}>
      <div className="cover-wrap">
        <div className="logo-wrap">
          <img src="https://cms.ghadiscovery.com/content/download/281/1261?version=31&inline=1" alt="" />
        </div>
        
      </div>
      <div className="tag-wrap">
        <p>签名奢侈品</p>
      </div>
      <div className="info-wrap">
        <div className="brand">凯宾斯基酒店</div>
        <h2 className="title">凯宾斯基大道酒店</h2>
        <div className="spt-line"></div>
        <div className="mt-3">
            <div className="discount-line">
                <i className="iconfont icon-Location"></i> 
                bejing, China
            </div>
            <div class="discount-line">
                <i class="iconfont icon-Calendar"></i> 
                预订期限至：2025-02-02 12:00
            </div>
            <div class="discount-line">
                <i class="iconfont icon-Room"></i> 
                住宿期限：2025-02-02 至 2025-02-03
            </div>
        </div>
      </div>
    </div>
  )
}