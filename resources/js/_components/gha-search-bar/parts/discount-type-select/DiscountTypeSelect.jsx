import { Popover } from "antd"
import { useEffect, useState } from "react"
import mockjs from "mockjs"

function getRandomTags(len) {
  return new Array(len).fill(0).map((_, index) => {
    return {
      id: Math.random().toString(36).substr(2, 9),
      name: mockjs.Random.cword(2, 5)
    }
  })
}

export default function DiscountTypeSelect() {
  const [contentWidth, setContentWidth] = useState(0)
  const [open, onOpenChange] = useState(false)
  const types = [
    {id: "all", name: "所有"},
    ...getRandomTags(10)
  ]
  const content = (
    <div className={``} style={{width: `${contentWidth}px`}}>
      {types.map(e => {
        return (
          <div onClick={() => onOpenChange(false)} key={e.id} className="py-2 border-t first:border-t-0 cursor-pointer" >{e.name}</div>
        )
      })}
    </div>
  )
  useEffect(() => {
    const targetElement = document.querySelector('.discount-type-select').parentNode;
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const width = entry.contentRect.width;
        setContentWidth(width);
      }
    });
    resizeObserver.observe(targetElement);
  }, [])
  return (
    <Popover 
      trigger="click"
      placement="bottom"
      content={content}
      arrow={false}
      open={open}
      onOpenChange={onOpenChange}
      rootClassName="gha-popover-root"
    >
      <div className="discount-type-select search-item flex flex-row items-center justify-between pl-1 cursor-pointer">
        <p className="">选择优惠类型</p>
        <i className="iconfont icon-down"></i>
      </div>
    </Popover>
  )
}