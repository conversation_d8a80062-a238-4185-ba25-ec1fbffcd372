import { useState, useEffect, useRef } from "react"
import { Popover } from "antd"
import dayjs from 'dayjs';
import PeopleSelect, { formatPeople } from "./parts/people-select/PeopleSelect"
import DateSelect from "./parts/date-select/DateSelect"
import KeywordInput from "./parts/keyword-input/KeywordInput"
import DiscountCodeInput from "./parts/discount-code-input/DiscountCodeInput"
import DiscountTypeSelect from "./parts/discount-type-select/DiscountTypeSelect"
import DiscountHotelSelect from "./parts/hotel-select/HotelSelect"
import useGhaSearchBarStore, { defaultRooms } from "@/_components/gha-search-bar/useGhaSearchBarStore"
import { $helper } from "@/_utils/_index"

function useSearchBarScrollFixed(wrapperEl, sticky = false) {
  useEffect(() => {
    if (!sticky) return
    const targetElement = $(wrapperEl.current).find(".fixed-content")[0]
    if (!targetElement) return;
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const height = entry.contentRect.height;
        if ($(wrapperEl.current).find(".fixed-holder")[0]) {
          $(wrapperEl.current).find(".fixed-holder")[0].style.height = `${height}px`;
        }
      }
    });
    resizeObserver.observe(targetElement);
  }, [])
  useEffect(() => {
    if (!sticky) return
    const sentinelTop = $(wrapperEl.current).find('.fixed-sentinel-top')[0];
    const sentinelBottom = $(wrapperEl.current).find('.fixed-sentinel-bottom')[0];
    const target = $(wrapperEl.current)[0];
    if (!target || !sentinelTop || !sentinelBottom) return;
    window.addEventListener('scroll', () => {
      if ((window.innerWidth >= 1024 && sentinelTop.getBoundingClientRect().top < 90) || (window.innerWidth < 1024 && sentinelBottom.getBoundingClientRect().top < 120)) {
        target.classList.add('gha-fixed');
      } else {
        target.classList.remove('gha-fixed');
      }
    })
  }, [])
  return null
}



export const GhaIndexSearchBar = () => {
  const {keyword, rooms, date, promoCode} = useGhaSearchBarStore()
  function onSearch() {
    if (!keyword.trim()) return
    console.error("onSearch not implemented", keyword, rooms, date, promoCode)
    const searchParams = {
      keyword,
      rooms: JSON.stringify(rooms),
      date: date.map(e => e.format("YYYY-MM-DD")),
      promoCode
    }
    location.href = `/search/hotels#/?${$helper.encodeSearchHash(searchParams)}`
  }
  return (
    <div className="gha-search-bar-wrap index">
      <div className="fixed-sentinel"></div>
      <div className="fixed-holder"></div>
      <div className="fixed-content">
        <div className="px-4 lg:px-0 bg-white lg:flex lg:flex-row">
          <div className="w-full lg:w-56 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r relative"><KeywordInput/></div>
          <div className="w-full lg:w-72 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r"><DateSelect/></div>
          <div className="flex flex-row border-b lg:border-b-0 lg:flex-1">
            <div className="flex-1 h-[48px] lg:px-2 lg:h-[78px] lg:border-r"><PeopleSelect/></div>
            <div className="right-split mx-2 lg:hidden"></div>
            <div className="flex-1 h-[48px] lg:px-2 xl:flex-none xl:w-40 lg:h-[78px]"><DiscountCodeInput/></div>
          </div>
          <div className="pb-3 lg:pb-0">
            <a onClick={onSearch} href="javascript:;" className={`${keyword.trim() ? "" : "opacity-50 cursor-not-allowed"} gha-primary-btn w-full lg:h-full lg:!font-16 lg:!px-12 lg:!py-0 mt-3 lg:mt-0 lg:!rounded-none lg:flex lg:items-center lg:justify-center`}>查找酒店</a>
          </div>
        </div>
      </div>
    </div>
  )
}

export const GhaHotelListSearchBar = ({className, sticky, onSubmit}) => {
  const {rooms, date, keyword} = useGhaSearchBarStore()
  const wrapperElRef = useRef(null)
  useSearchBarScrollFixed(wrapperElRef, sticky)
  useEffect(() => {
    window.addEventListener('scroll', function() {
      setMobilePopoverOpend(false)
    })
  }, [])
  useEffect(() => {
    const hash = location.hash.split("?")[1]
    const hashParams = $helper.decodeSearchHash(hash)
    let date = [dayjs().add(30, 'day'), dayjs().add(32, 'day')]
    try {
      date = hashParams.date.split(",").map(e => dayjs(e))
    } catch (e) {}
    let rooms = [...defaultRooms]
    try {
      rooms = JSON.parse(hashParams.rooms)
    } catch (e) {}
    useGhaSearchBarStore.setState({keyword: hashParams.keyword || "", date, rooms})
  }, [])
  function _onSubmit() {
    if (!keyword.trim()) return
    onSubmit({rooms, date, keyword})
  }
  const [mobilePopoverOpend, setMobilePopoverOpend] = useState(false)
  return (
    <div ref={wrapperElRef} className={`gha-search-bar-wrap ${className || ""}`}>
      <div className="fixed-sentinel-top"></div>
      <div className="fixed-holder"></div>
      <div className="fixed-mobile">
        <Popover placement="bottom" 
          content={(
            <div className="w-screen">
              <GhaHotelListSearchBar onSubmit={onSubmit}/>
            </div> 
          )}
          trigger={["click"]}
          arrow={false}
          rootClassName="gha-popover-root-no-padding"
          open={mobilePopoverOpend}
          onOpenChange={(open) => {
            if ($helper.isAntdGhaPopoverRootVisible()) return
            setMobilePopoverOpend(open)
          }}
        >
          <div  
            className="fixed top-[90px] left-0 right-0 flex flex-row items-center px-4 h-[56px] z-50 bg-white border-b">
            <div className="">{date[0].format("YYYY-MM-DD")} - {date[1].format("YYYY-MM-DD")}</div>
            <div className="flex-1"></div>
            <p>{formatPeople(rooms)}</p>
            <i className="iconfont icon-edit ml-1 !text-xl"></i>
          </div>
        </Popover>
        
      </div>
      <div className="fixed-content">
        <div className="px-4 lg:px-0 bg-white lg:flex lg:flex-row">
          <div className="w-full lg:w-auto lg:flex-1 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r"><KeywordInput/></div>
          <div className="w-full lg:w-auto lg:flex-1 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r"><DateSelect/></div>
          <div className="flex flex-row border-b lg:border-b-0 lg:flex-1">
            <div className="flex-1 h-[48px] lg:px-2 lg:h-[78px] lg:border-r"><PeopleSelect/></div>
          </div>
          <div className="pb-3 lg:pb-0">
            <a onClick={_onSubmit} href="javascript:;" className={`${keyword.trim() ? "" : "opacity-50 cursor-not-allowed"} gha-primary-btn w-full lg:h-full lg:!font-16 lg:!px-12 lg:!py-0 mt-3 lg:mt-0 lg:!rounded-none lg:flex lg:items-center lg:justify-center`}>查找酒店</a>
          </div>
        </div>
      </div>
      <div className="fixed-sentinel-bottom"></div>
    </div>
  )
}

export const GhaDiscountSearchBar = ({className, sticky}) => {
  const wrapperElRef = useRef(null)
  useSearchBarScrollFixed(wrapperElRef, sticky)
  return (
    <div ref={wrapperElRef} className={`gha-search-bar-wrap ${className || ""}`}>
      <div className="fixed-sentinel-top"></div>
      <div className="fixed-holder"></div>
      <div className="fixed-mobile">
        <div onClick={() => window.scrollTo(0, 0)} className="fixed top-[90px] left-0 right-0 flex flex-row items-center px-4 h-[56px] z-50 bg-white shadow-md border-b">
          <div className="">2025/09/06 - 2025/09/08</div>
          <div className="flex-1"></div>
          <p>2房间1成人1儿童 </p>
          <i className="iconfont icon-edit ml-1 !text-xl"></i>
        </div>
      </div>
      <div className="fixed-content">
        <div className="px-4 lg:px-0 bg-white lg:flex lg:flex-row">
          <div className="w-full lg:w-auto lg:flex-1 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r"><KeywordInput/></div>
          <div className="w-full lg:w-auto lg:flex-1 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r relative"><DiscountTypeSelect/></div>
          <div className="w-full lg:w-auto lg:flex-1 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r"><DateSelect/></div>
          <div className="pb-3 lg:pb-0">
            <a href="javascript:;" className="gha-primary-btn !border-[#8bbcd9] !bg-[#8bbcd9] w-full lg:h-full lg:!font-16 lg:!px-12 lg:!py-0 mt-3 lg:mt-0 lg:!rounded-none lg:flex lg:items-center lg:justify-center">查找优惠</a>
          </div>
        </div>
      </div>
      <div className="fixed-sentinel-bottom"></div>
    </div>
  )
}

export const GhaDiscountDetailBar = ({className, sticky}) => {
  const [rooms, setRooms] = useState([{adult: 2, child: 0, childAges: []}])
  const wrapperElRef = useRef(null)
  return (
    <div ref={wrapperElRef} className={`gha-search-bar-wrap ${className || ""}`}>
      <div className="fixed-sentinel-top"></div>
      <div className="fixed-holder"></div>
      <div className="fixed-content">
        <div className="bg-white lg:flex lg:flex-row">
          <div className="w-full lg:w-auto lg:flex-1 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r"><DiscountHotelSelect/></div>
          <div className="w-full lg:w-auto lg:flex-1 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r"><DateSelect/></div>
          <div className="flex flex-row border-b lg:border-b-0 lg:flex-1">
            <div className="flex-1 h-[48px] lg:px-2 lg:h-[78px]"><PeopleSelect rooms={rooms} setRooms={setRooms}/></div>
          </div>
          <div className="hidden lg:flex items-center">
            <div className="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center cursor-pointer">
              <i className="iconfont icon-Search"></i>
            </div>
          </div>
          <div className="py-2 lg:hidden">
            <div className="gha-primary-btn w-full cursor-pointer">立即预订</div>
          </div>
        </div>
      </div>
      <div className="fixed-sentinel-bottom"></div>
    </div>
  )
}

export const GhaHotelDetailSearchBar = ({className, sticky}) => {
  const wrapperElRef = useRef(null)
  useSearchBarScrollFixed(wrapperElRef, sticky)
  useEffect(() => {
    window.addEventListener('scroll', function() {
      setMobilePopoverOpend(false)
    })
  }, [])
  const [mobilePopoverOpend, setMobilePopoverOpend] = useState(false)
  return (
    <div ref={wrapperElRef} className={`gha-search-bar-wrap ${className || ""}`}>
      <div className="fixed-sentinel-top"></div>
      <div className="fixed-holder"></div>
      <div className="fixed-mobile">
        <Popover placement="bottom" 
          content={(
            <div className="w-screen">
              <GhaHotelListSearchBar/>
            </div> 
          )}
          trigger={["click"]}
          arrow={false}
          rootClassName="gha-popover-root-no-padding"
          open={mobilePopoverOpend}
          onOpenChange={(open) => {
            if ($helper.isAntdGhaPopoverRootVisible()) return
            setMobilePopoverOpend(open)
          }}
        >
          <div  
            className="fixed top-[90px] left-0 right-0 flex flex-row items-center px-4 h-[56px] z-50 bg-white border-b">
            <div className="">2025/09/06 - 2025/09/08</div>
            <div className="flex-1"></div>
            <p>2房间1成人1儿童 </p>
            <i className="iconfont icon-edit ml-1 !text-xl"></i>
          </div>
        </Popover>
      </div>
      <div className="fixed-content">
        <div className="px-4 lg:px-0 bg-white lg:flex lg:flex-row">
          <div className="w-full lg:w-auto lg:flex-1 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r"><DateSelect/></div>
          <div className="flex flex-row border-b lg:border-b-0 lg:flex-1">
            <div className="flex-1 h-[48px] lg:px-2 lg:h-[78px] lg:border-r"><PeopleSelect/></div>
          </div>
          <div className="w-full lg:w-auto lg:flex-1 lg:px-2 h-[48px] lg:h-[78px] border-b lg:border-b-0 lg:border-r"><DiscountCodeInput/></div>
          <div className="pb-3 lg:pb-0">
            <a href="javascript:;" className="gha-primary-btn w-full lg:h-full lg:!font-16 lg:!px-12 lg:!py-0 mt-3 lg:mt-0 lg:!rounded-none lg:flex lg:items-center lg:justify-center">立即预订</a>
          </div>
        </div>
      </div>
      <div className="fixed-sentinel-bottom"></div>
    </div>
  )
}