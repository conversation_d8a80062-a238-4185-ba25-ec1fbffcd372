import { createRoot } from "react-dom/client";
import { Button, Form, Input, Radio, ConfigProvider, Select, Checkbox } from 'antd';
import GhaConfigProvider, { useAntdMessage, useForceUpdate } from "@/_components/GhaConfigProvider";
import { useEffect, useRef, useState, useCallback } from "react";
import { $http, $constants, $helper } from "@/_utils/_index"

function RegisterForm() {
  const [step, setStep] = useState(1)
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false)
  const message = useAntdMessage()
  const forceUpdate = useForceUpdate()
  const [registerData, setRegisterData] = useState(null)
  const interval = useRef(null)
  const intervalCount = useRef(5)
  function onFinish(values) {
    setLoading(true)
    $http.authRegister(values).subscribe(res => {
      setLoading(false)
      if (res.status_code !== 200) {
        message.error(res.message)
        return
      }
      setRegisterData(res.data)
      setStep(2)
      startInterval()
    })
  }
  useEffect(() => {
    return () => {
      interval.current && clearInterval(interval.current)
    }
  }, [])

  function startInterval() {
    interval.current = setInterval(() => {
      intervalCount.current -= 1
      forceUpdate()
      if (intervalCount.current === 1) {
        clearInterval(interval.current)
        window.location.href = document.referrer || "/"
      }
    }, 1000)
  }
  return step === 1 ? (
    <>
      <Form
        layout="vertical"
        form={form}
        onFinish={onFinish}
        requiredMark={false}
        validateTrigger="onBlur"
        initialValues={$helper.isLaravelLocal() ? {
          first_name: "wu",
          last_name: "wchy",
          email: "<EMAIL>",
          password: "12345Abc$",
          confirm_password: "12345Abc$",
          city: "西安",
          country: 49,
          is_message: true,
        } : {}}
      >
        <div className="flex flex-col lg:flex-row">
          <Form.Item label="姓*" name="first_name" className='flex-1 lg:mr-5' rules={[{required: true, message: "请输入您的姓"}]}>
            <Input variant='underlined' placeholder="请输入您的姓" />
          </Form.Item>
          <Form.Item label="名*" name="last_name" className='flex-1' rules={[{required: true, message: "请输入您的名"}]}>
            <Input variant='underlined' placeholder="请输入您的名" />
          </Form.Item>
        </div>

        <Form.Item label={<>邮箱<span className='text-[#919191]/60'>（接收订单确认函，请准确填写）</span>*</>} name="email" 
          rules={[
            {required: true, message: "请输入您的邮箱"},
            { 
              pattern: $constants.emailPattern,
              message: "邮箱格式不正确"
            }
          ]}
        >
          <Input variant='underlined' placeholder="请输入您的邮箱" />
        </Form.Item>

        <div className="flex flex-col lg:flex-row">
          <Form.Item label="密码*" name="password" className='flex-1 lg:mr-5' 
            rules={[
              {required: true, message: "请输入密码"},
              { 
                pattern: $constants.passwordPattern,
                message: "密码至少8位，包含字母、数字和符号，且首尾不能有空格"
              }
            ]}
          >
            <Input.Password variant='underlined' placeholder="请输入密码" />
          </Form.Item>
          <Form.Item label="确认密码*" name="confirm_password" className='flex-1' 
            dependencies={['password']} 
            rules={[
              {required: true, message: "请输入确认密码"},
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('密码与确认密码不一致!'));
                },
              }),
            ]}
          >
            <Input.Password variant='underlined' placeholder="请输入确认密码"/>
          </Form.Item>
        </div>
        <div className="flex flex-col lg:flex-row">
          <Form.Item label="城市*" name="city" className='flex-1 lg:mr-5' rules={[{required: true, message: "请输入城市"}]}>
            <Input variant='underlined' placeholder="请输入城市" />
          </Form.Item>
          <Form.Item label="国家*" name="country" className='flex-1' rules={[{required: true, message: "请选择国家"}]}>
            <Select variant='underlined' placeholder="请选择国家">
              {window.__ServerVars__.countryList.map(item => {
                return (
                  <Select.Option key={item.id} value={item.id}>{item.country_name}</Select.Option>
                )
              })}
            </Select>
          </Form.Item>
        </div>
        <div className="tip-wrap">
          <h5>不要错过 GHA DISCOVERY 通过电子邮件发送的限时优惠、闪购个性化更新以及特别酒店和合作伙伴促销活动。 按照下面链接的隐私政策中的说明随时取消订阅。</h5>
          <Form.Item label={null} name="is_message" className='mt-2' rules={[{required: false}]}>
            <Radio.Group size='small' style={{display: "flex", flexDirection: "column", gap: 7}}>
              <Radio value={true}>是的，我希望独家获取新闻和优惠信息。</Radio>
              <Radio value={false}>不，我不需要最新消息和优惠。</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item valuePropName="checked" label={null} name="is_auth" className='-mt-4' rules={[{required: true, message: "请同意隐私政策"}]}>
            <Checkbox>同意将个人数据根据GHA隐私政策中国附录的条款转移到中国境外。</Checkbox>
          </Form.Item>
          <Form.Item hidden name="language" initialValue={"ZH"}>
            <Input />
          </Form.Item>
          <p className='-mt-3'>点击“立即注册”，即表示我接受 GHA DISCOVERY 计划的 <a href="">条款和条件</a> 以及 <a href="">隐私政策</a>。</p>

          <div className="submit-wrap">
            <Button loading={loading} type="primary" shape="round" block htmlType="submit">注册</Button>
            <p><a href={window.__ServerVars__.activeUri} className='underline'>激活您的在线帐户</a></p>
            <p>已有账号，<a href={window.__ServerVars__.loginUri}>立即登录</a></p>
          </div>
        </div>
      </Form>
    </>
  ) : (
    <>
      <div className="flex flex-col items-center justify-center text-center h-full">
        <i className='iconfont icon-a-14Benefits_brand_benefits text-primary text-4xl'></i>
        <h2 className="mt-1 font-18 font-bold">注册成功</h2>
        <h5 className="mt-4 pb-20 font-12">
          感谢您选择 GHA 会员计划<br/>请登录您的邮箱：{registerData?.user?.email} 进行账号激活
        </h5>
        <p className="font-12 text-[#919191]"><span className="font-bold text-black">0{intervalCount.current}</span> 秒后返回上一页</p>
      </div>
    </>
  )
}

createRoot(document.querySelector(".form-wrap")).render(<GhaConfigProvider><RegisterForm /></GhaConfigProvider>);