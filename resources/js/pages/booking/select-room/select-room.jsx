import { createRoot } from "react-dom/client";
import IndexFilterApp from "@/pages/index/jsx/index-filter/App"
import OrderStep from "./parts/OrderStep";
import RoomItem from "./parts/RoomItem";
import { useState } from "react";

function App() {
  const [isEdit] = useState(true);
  return (
    <>
      <div className="hotel-top gha-bg-test">
        <div className="absolute top-0 left-0 right-0 bottom-0 z-10" style={{ backgroundImage: `linear-gradient(90deg, rgba(0, 93, 121, 1) 50%, rgba(111, 202, 221, 0) 80%)` }}></div>
        <div className="g-main-content-sm relative z-20">
          <div className="flex flex-row items-center">
            <div className="logo">
              <img src="https://cms.ghadiscovery.com/content/download/560/2541?version=27&inline=1" alt=""/>
            </div>
            <h1>长沙玛珂酒店</h1>
          </div>
          {isEdit ? (
            <div className="edit-tip">
              <p>重新选择房型</p>
              <p>订单号：GHA12345678</p>
            </div>
          ) : (
            <div className="back">
              <a href="javascript:;"><i className="iconfont icon-left"></i>返回</a>
            </div>
          )}
          
        </div>
      </div>
      <div className="g-main-content-sm">
        <div className="home-filter-wrap">
          <div className="inner">
            <div className="filter-bar-wrap-holder">
              <div className="filter-bar-wrap small-filter"><IndexFilterApp/></div>
            </div>
          </div>
        </div>
      </div>
      <div className="py-12">
        <OrderStep/>
      </div>
      <div className="">
        <div className="g-main-content-sm">
          <div className="bg-[#bbb0dd]/80 rounded-xl text-white font-13 flex flex-row items-center px-3 md:px-8 py-3">
              <i className="iconfont icon-a-FrontDesk font-40 mr-2"></i>
              <div>
                  为符合条件的会员和另一位入住同一房间的客人提供免费早餐，提升您的住宿体验。直接预订时，探索之旅Titanium会员专享。<br/>
                  <a href="javascript:;" className="underline">条款和条件</a>适用。
              </div>
          </div>
        </div>
      </div>
      <div className="py-12">
        <div className="g-main-content-sm">
          {new Array(3).fill(0).map((_, index) => {
            return (
              <RoomItem key={index}/>
            )
          })}
        </div>
      </div>
      <div className="text-center">
        <a href="javascript:;" className="gha-btn">显示更多</a>
      </div>
    </>
  )
}

createRoot(document.querySelector(".select-room-content")).render(<App />);