import { createRoot } from "react-dom/client";
import { useEffect, useState, useRef } from "react";
import TopFilter from "./parts/TopFilter";
import TagFilter from "./parts/TagFilter";
import FilterState, {orderOptions} from "./parts/FilterState";
import HotelList from "./parts/HotelList";
import HotelMap from "./parts/HotelMap";
import MobileFilterBar from "./parts/MobileFilterBar";
import _MockHotelDataSource, { MockHotelDataSource3 } from "./parts/mock-hotel"
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"
import { $http, $helper } from "@/_utils/_index"


function HotelListApp() {
  const mapRef = useRef(null)
  const showType = useSearchStore(state => state.showType)
  const slug = useSearchStore(state => state.slug)
  const list = useSearchStore(state => state.list)
  const [dataSource, setDataSource] = useState(() => {
    return _MockHotelDataSource.slice(0, 1).map(e => ({...e, liked: false}))
  })
  useEffect(() => {
    // console.error("HotelListApp", useGhaSearchBarStore.getState())
    requestData()
  }, [])
  function onHotelLike(id, callback) {
    $http.hook2000().subscribe(
      res => {
        const newDataSource = [...dataSource]
        const item = newDataSource.find(e => `${e.id}` === `${id}`)
        item.liked = !item.liked
        setDataSource(newDataSource)
        callback(item.liked, newDataSource)
      }
    )
  }
  function onCondChange(cond) {
    console.error("onCondChange", cond)
    if (cond.type === "search") {
      const searchBarStoreState = useGhaSearchBarStore.getState()
      const searchParams = {
        keyword: searchBarStoreState.keyword,
        rooms: JSON.stringify(searchBarStoreState.rooms),
        date: searchBarStoreState.date.map(e => e.format("YYYY-MM-DD"))
      }
      location.href = `/search/${slug}#/?${$helper.encodeSearchHash(searchParams)}`
    }
    requestData()
  }
  function buildSearchParams() {
    const searchBarStoreState = useGhaSearchBarStore.getState()
    const searchStoreState = useSearchStore.getState()
    const selectTags = searchStoreState.selectTags.filter(e => e.indexOf("_all") < 0)
    const searchParams = {
      keyword: searchBarStoreState.keyword,
      rooms: JSON.stringify(searchBarStoreState.rooms),
      date: searchBarStoreState.date.map(e => e.format("YYYY-MM-DD")),
      order: searchStoreState.order,
      brands: selectTags.filter(e => e.startsWith("brands_")).map(e => e.split("_")[1]).join("_"),
      categories: selectTags.filter(e => e.startsWith("categories_")).map(e => e.split("_")[1]).join("_"),
      facts: selectTags.filter(e => e.startsWith("facts_")).map(e => e.split("_")[1]).join("_"),
      series: selectTags.filter(e => e.startsWith("series_")).map(e => e.split("_")[1]).join("_"),
    }
    return searchParams
  }
  function requestData() {
    useSearchStore.setState({loading: true})
    $http.searchHotels(buildSearchParams()).subscribe(res => {
      useSearchStore.setState({loading: false, list: res.data.data})
      mapRef.current?.updateMapData(res.data.data)
      // console.error("xxxxxxx", res)
    })
  }
  return (
    <>
      <TopFilter onCondChange={onCondChange} />
      <div className="hidden lg:block relative">
        <TagFilter onCondChange={onCondChange} />
        <FilterState onCondChange={onCondChange}/>
      </div>
      <div className="lg:hidden relative">
        <MobileFilterBar/>
      </div>
      {showType === "list" && <HotelList></HotelList>}
      {showType === "map" && <HotelMap ref={mapRef} dataSource={list} onHotelLike={onHotelLike}></HotelMap>}
    </>
  )
}

createRoot(document.querySelector(".hotel-list-page-wrap")).render(<HotelListApp />);