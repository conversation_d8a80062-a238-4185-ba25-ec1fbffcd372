import { useEffect, useState } from "react"
import HotelItem from "@/_components/HotelItem"
import OfferItem from "@/_components/OfferItem"
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";

const pageSize = 18
export default function HotelList() {
  // const [list, slug, loading] = useSearchStore(state => [state.list, state.slug, state.loading])
  const list = useSearchStore(state => state.list)
  const slug = useSearchStore(state => state.slug)
  const loading = useSearchStore(state => state.loading)
  const [curPage, setCurPage] = useState(1)
  function loadMore() {
    setCurPage(curPage + 1)
  }
  useEffect(() => {
    if (loading) {
      setCurPage(1)
    }
  }, [loading])
  return (
    <div className="">
      <div className="g-main-content min-h-64">
        {loading ? (
          <div className="py-8">
            <div className="w-10 h-10 mx-auto flex items-center justify-center loading-ami"><i className="iconfont icon-loading text-4xl"></i></div>
          </div>
        ) : (
          <div className="flex flex-row flex-wrap -mx-3">
            {list.slice(0, curPage * pageSize).map(hotel => {
              return (
                <div key={hotel.id} className="px-3 w-full md:w-1/2 lg:w-1/3 mb-6">
                  {slug === "hotels" && <HotelItem className="hotel-item-large" hotel={hotel}></HotelItem>}
                  {slug === "offers" && <OfferItem className="hotel-item-large"></OfferItem>}
                </div>
              )
            })}
          </div>
        )}
        
        {(curPage * pageSize < list.length && !loading) && (
          <div className="text-center py-4">
            <a onClick={loadMore} href="javascript:;" className="gha-btn !px-8">显示更多</a>
          </div>
        )}
      </div>
    </div>
    
  )
}