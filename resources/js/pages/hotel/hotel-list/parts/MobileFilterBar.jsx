import React, { useEffect, useRef, useState } from 'react'
import mockjs from 'mockjs'
import MobileFilterModal from './MobileFilterModal'
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";

function getRandomTags(len) {
  return new Array(len).fill(0).map((_, index) => {
    return {
      id: Math.random().toString(36).substr(2, 9),
      name: mockjs.Random.cword(2, 5)
    }
  })
}

export default function MobileFilterBar() {
  const targetRef = useRef(null);
  const sentinelRef = useRef(null);
  const [modalOpend, setModalOpend] = React.useState(false);
  const [filterSection] = useState(() => {
    return [
      {
        type: "category", 
        name: "酒店类型", 
        tags: [{id: "all", name: "显示全部"}].concat(getRandomTags(5))
      },
      {
        type: "brands", 
        name: "酒店品牌", 
        tags: [{id: "all", name: "显示全部"}].concat(getRandomTags(30))
      },
      {
        type: "loop", 
        name: "酒店系列", 
        tags: [{id: "all", name: "显示全部"}].concat(getRandomTags(10))
      },
      {
        type: "roms", 
        name: "酒店设施", 
        tags: [{id: "all", name: "显示全部"}].concat(getRandomTags(10))
      },
    ]
  })
  useEffect(() => {
    window.addEventListener('scroll', () => {
      if (!sentinelRef.current || !targetRef.current) return;
      if (window.innerWidth < 1024 && sentinelRef.current.getBoundingClientRect().top < 146) {
        targetRef.current.classList.add('gha-fixed');
      } else {
        targetRef.current.classList.remove('gha-fixed');
      }
    })
  }, [])
  return (
    <>
      <div className="mobile-filter-bar">
        <div className="g-main-content">
          <div ref={sentinelRef} className="h-[56px]">
            <div ref={targetRef} className="flex flex-row items-center justify-between h-[56px] font-14">
              <p onClick={() => setModalOpend(true)} className="flex flex-row items-center cursor-pointer">筛选与排序 <i className="iconfont icon-filter ml-1 text-lg"></i></p>
              <p onClick={() => useSearchStore.setState({showType: "map"})} className="flex flex-row items-center cursor-pointer">显示地图 <i className="iconfont icon-Map ml-1 text-2xl"></i></p>
            </div>
          </div>
        </div>
      </div>
      <MobileFilterModal modalOpend={modalOpend} setModalOpend={setModalOpend}/>
    </>
  )
}