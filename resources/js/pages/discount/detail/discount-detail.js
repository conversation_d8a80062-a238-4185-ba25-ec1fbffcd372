
$(function() {
  new DiscountDetailController()
})
class DiscountDetailController {
  constructor() {
    this.bootstrap()
  }

  bootstrap() {
    this.initFixedBar(".fixed-bar")
    this.initFixedBar(".fixed-bar-m")
    this.bindEvents()
    this.initScrollActive()
  }

  bindEvents() {
    $("body").on("click", "#togglePolicy", function() {
      if ($(this).find("i").hasClass("icon-down")) {
        $(this).find("i").removeClass("icon-down").addClass("icon-up")
      } else {
        $(this).find("i").removeClass("icon-up").addClass("icon-down")
      }
      $("#togglePolicyContent").toggleClass("hidden")
    })
    $("body").on("click", ".anchor-item", function() {
      $(".anchor-item").removeClass("active")
      $(this).addClass("active")
    })
  }

  initFixedBar(selector) {
    const sentinelTop = $(selector).find(`.fixed-sentinel-top`)[0];
    const target = $(selector)[0];
    if (!target || !sentinelTop) return;
    function handleScroll() {
      if (sentinelTop.getBoundingClientRect().top < 90) {
        target.classList.add('gha-fixed');
      } else {
        target.classList.remove('gha-fixed');
      }
    }
    window.addEventListener('scroll', handleScroll, true)
    handleScroll()
  }

  initScrollActive() {
    function setTabItemActive(idx) {
      if (!$(".fixed-bar .anchor-item").eq(idx).hasClass("active")) {
        $(".fixed-bar .anchor-item").removeClass("active")
        $(".fixed-bar .anchor-item").eq(idx).addClass("active")
      }
      if (!$(".fixed-bar-m .anchor-item").eq(idx).hasClass("active")) {
        $(".fixed-bar-m .anchor-item").removeClass("active")
        $(".fixed-bar-m .anchor-item").eq(idx).addClass("active")
      }
    }
    function handleScroll() {
      let anchor0Top = document.querySelector("#anchor0").getBoundingClientRect().top
      let anchor1Top = document.querySelector("#anchor1").getBoundingClientRect().top
      let anchor2Top = document.querySelector("#anchor2").getBoundingClientRect().top
      let topIdx = [anchor0Top, anchor1Top, anchor2Top].findIndex(e => e > 10)
      if (topIdx === -1) {
        setTabItemActive(2)
      }
      if (2 === topIdx) {
        setTabItemActive(1)
      }
      if ([0, 1].includes(topIdx)) {
        setTabItemActive(0)
      }
    }
    window.addEventListener('scroll', handleScroll)
  }
}