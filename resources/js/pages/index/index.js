
$(function() {
  new IndexController()
})
class IndexController {
  constructor() {
    this.bootstrap()
  }

  bootstrap() {
    this.initTopBanner()
    this.initFilterTab()
    this.initAccountSwiper()
  }

  initTopBanner() {
    new Swiper(".home-banner-wrap .swiper")
  }

  initAccountSwiper() {
    new Swiper(".account-section-wrap .swiper-lg", {
      slidesPerView: "auto",
      spaceBetween: 16,
      centeredSlides: true,
      
      // loop: true,
      navigation: {
        nextEl: ".account-section-wrap .gha-swiper-button-next",
        prevEl: ".account-section-wrap .gha-swiper-button-prev",
      },
      on: {
        slideChange: function() {
          $(".account-section-wrap .active-info-wrap").addClass("hidden")
          $(".account-section-wrap .active-info-wrap").eq(this.activeIndex).removeClass("hidden")
          // $(".account-section-wrap .swiper-m").swiper("slideTo", this.activeIndex)
        },
      }
    })
    new Swiper(".account-section-wrap .swiper-m", {
      slidesPerView: "1.2",
      spaceBetween: 16,
      slidesOffsetBefore: 16,
      pagination: {
        el: ".swiper-pagination.account-swiper-m",
        clickable: true,
      },
    })
  }

  initFilterTab() {
    $("body").on("click", ".home-filter-wrap .tab-list .item", function() {
      let idx = $(this).index()
      $(".home-filter-wrap .tab-list").removeClass("active-1").removeClass("active-2").removeClass("active-3").addClass(`active-${idx + 1}`)
    })
  }
}