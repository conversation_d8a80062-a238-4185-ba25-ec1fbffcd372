
$(function() {
  new AboutIndexController()
})
class AboutIndexController {
  constructor() {
    this.bootstrap()
  }

  bootstrap() {
    this.initBrandSwiper()
    this.initShowMoreBrand()
    this.initBrandDetailDescTab()
    this.initBrandDetailSwiper()
  }

  initBrandDetailSwiper() {
    // $('.brands-hotel-swiper').slick({
    //   slidesToShow: 3,
    //   slidesToScroll: 1,
    //   focusOnSelect: true,
    //   centerPadding: 24,
    // });
    // new Swiper(".brands-hotel-swiper .swiper-container", {
    //   slidesPerView: 3,
    //   spaceBetween: 24,
    //   centeredSlides: false,
    //   // slidesOffsetBefore: 20,
    //   // slidesOffsetAfter: 20,
    // })
  }

  initBrandDetailDescTab() {
    $(".brand-desc-tab-item a").on("click", function() {
      if ($(this).hasClass("active")) return
      const idx = $(this).parent().index()
      console.error("ddddd", idx)
      $(".brand-desc-tab-item a").removeClass("active")
      $(this).addClass("active")
      $(".brand-desc-tab-panel").addClass("hidden")
      $(".brand-desc-tab-panel").eq(idx).removeClass("hidden")
    })
  }

  initShowMoreBrand() {
    $(".show-more-brand-btn").on("click", function() {
      $(".brand-list-toggle").addClass("show-more")
      $(this).hide()
    })
  }

  initBrandSwiper() {
    new Swiper(".lg-swiper-el .swiper-container", {
      navigation: {
        nextEl: ".lg-swiper-el .gha-swiper-button-next",
        prevEl: ".lg-swiper-el .gha-swiper-button-prev",
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
    })
    new Swiper("#md-swiper")
  }

  initAccountSwiper() {
    new Swiper(".account-section-wrap .swiper-lg", {
      slidesPerView: "auto",
      spaceBetween: 16,
      centeredSlides: true,
      
      // loop: true,
      navigation: {
        nextEl: ".account-section-wrap .gha-swiper-button-next",
        prevEl: ".account-section-wrap .gha-swiper-button-prev",
      },
      
    })
    new Swiper(".account-section-wrap .swiper-m", {
      slidesPerView: "1.2",
      spaceBetween: 16,
      slidesOffsetBefore: 16,
    })
  }
}