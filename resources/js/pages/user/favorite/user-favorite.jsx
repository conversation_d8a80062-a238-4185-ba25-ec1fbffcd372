import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input, ConfigProvider, Tabs, Checkbox } from 'antd';
import GhaConfigProvider from "@/_components/GhaConfigProvider"
import mockjs from "mockjs";
import HotelItem from "@/_components/HotelItem";


export default function UserFavorite() {
  return (
    <>
      <div className="pb-5 lg:px-10 lg:mt-4 lg:shadow-[0px_5px_5px_0px_rgba(0,0,0,0.03)] border-b border-[#919191]/20">
        {/* <Checkbox.Group value={[1,2]}>
          <Checkbox value={1}>酒店</Checkbox>
          <Checkbox value={2}>住宿优惠</Checkbox>
          <Checkbox value={3}>特色体验</Checkbox>
          <Checkbox value={4}>本地生活优惠</Checkbox>
          <Checkbox value={5}>促销活动</Checkbox>
        </Checkbox.Group> */}
        <div className="-mx-2.5 flex md:block">
          <a href="javascript:;" className="flex-1 gha-btn w-32 !py-1.5 mx-2.5">酒店</a>
          <a href="javascript:;" className="flex-1 gha-primary-btn !py-1.5 w-32 mx-2.5">住宿优惠</a>
        </div>
        
      </div>
      <div className="py-6 lg:p-10">
        <div>
          <h2 className="font-16 font-Jost-SemiBold font-bold text-center md:text-left">酒店</h2>
          <div className="flex flex-row flex-wrap -mx-2 -mt-2">
            {Array.from({ length: 5 }).map((_, idx) => {
              return (
                <div key={idx} className="px-2 w-full md:w-1/2 xl:w-1/3 mt-4">
                  <HotelItem ></HotelItem>
                </div>
              )
            })}
          </div>
        </div>
        <div className="w-full h-px bg-[#919191]/20 my-7.5"></div>
        <div>
          <h2 className="font-16 font-Jost-SemiBold font-bold">住宿优惠</h2>
          <div className="flex flex-row flex-wrap -mx-2 -mt-2">
            {Array.from({ length: 2 }).map((_, idx) => {
              return (
                <div key={idx} className="px-2 w-full md:w-1/2 xl:w-1/3 mt-4">
                  <HotelItem ></HotelItem>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </>
    
  )
}

createRoot(document.querySelector("#favorite")).render(
  <GhaConfigProvider><UserFavorite /></GhaConfigProvider>
);