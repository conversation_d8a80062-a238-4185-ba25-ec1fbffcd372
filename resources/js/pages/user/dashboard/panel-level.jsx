import {Divider} from "antd"
import card1 from "@images/membership/index/card-1.png"
import card2 from "@images/membership/index/card-2.png"
import card3 from "@images/membership/index/card-3.png"
import card4 from "@images/membership/index/card-4.png"
import { useState } from "react"


export default function PanelLevel() {
  const rights = [
    {icon: "icon-a-EarnDonEligibleSpend", title: "赚取DISCOVERY奖励金（D$） (4%)"},
    {icon: "icon-a-EarnDonEligibleSpend", title: "赚取DISCOVERY奖励金（D$）"},
    {icon: "icon-a-ComplimentaryWi-Fi", title: "免费Wifi"},
    {icon: "icon-a-Exclusiveoffer", title: "专属优惠"},
    {icon: "icon-Experiences", title: "特色体验"},
    {icon: "icon-Experiences", title: "本地优惠生活"},
    {icon: "icon-Our_Partners_Purple", title: "合作伙伴礼遇"},
  ]
  const [activeLevel, setActiveLevel] = useState("1")
  const [levels] = useState(() => {
    const images = [card1, card2, card3, card4]
    return window.memberShipRights.map((e, idx) => {
      return {
        ...e, image: images[idx]}
      }
    )
  }) 
  console.error(levels)
  return (
    <div className="pt-4 lg:pt-0 px-4 lg:px-10">
      <div className={`level-step-container active-${activeLevel}`}>
        <div className="line-track">
          <span></span>
        </div>
        <div className="level-list">
          {levels.map(e => {
            return (
              <div className="level-item" key={e.key}>
                <h3>{e.title}</h3>
                <div className="tracker" onClick={() => setActiveLevel(e.key)}><span></span></div>
                <div className="level-card">
                  <img src={e.image} alt="" />
                </div>
              </div>
            )
          })}
        </div>
        <div className="level-card-m">
          <img src={levels.find(e => e.key === activeLevel)?.image} alt="" />
        </div>
      </div>
      <div className="mt-8 pb-8 border-b border-[#919191]/20">
        <Divider plain className="driver-919191-20"><h4 className="font-bold font-12">{ levels.find(e => e.key === activeLevel)?.title }会员福利</h4></Divider>
        <div className="flex flex-row flex-wrap -mx-1.5 lg:-mx-2.5 -mt-2 lg:mt-8 px-4 lg:px-16">
          {(levels.find(e => e.key === activeLevel)?.rights || []).map((item, idx) => {
            return (
              <div key={idx} className="w-1/2 lg:w-1/4 flex flex-col items-center mt-4 px-1.5 lg:px-2.5">
                <div className={`w-11 h-11 rounded-full shadow-[0px_2px_4px_2px_rgba(0,0,0,0.05)] flex items-center justify-center ${item.active ? "bg-primary text-white" : ""}`}>
                  <i className={`iconfont ${item.icon} text-2xl`}></i>
                </div>
                <p className="text-center mt-2.5 font-12">{item.text}</p>
              </div>
            )
          })}
        </div>
      </div>
      <div className="pt-8 flex flex-row items-stretch justify-center pb-8 lg:pb-2.5 -mx-2.5">
          <div className="flex-1 lg:w-60 py-7.5 mx-2.5 rounded-3xl border border-[#f2f2f2] shadow-[0_2px_4px_0px_rgba(0,0,0,0.08)] flex flex-col items-center justify-center">
            <div className="font-IvyMode-Reg rounded-full w-20 h-20 border-4 border-[#919191]/20 flex flex-row items-center justify-center font-12">
              <span className="font-20">0</span>/2
            </div>
            <p className="mt-2.5 font-12">再预订2次住宿即可升级</p>
          </div>
          <div className="flex-1 lg:w-60 py-7.5 mx-2.5 rounded-3xl border border-[#f2f2f2] shadow-[0_2px_4px_0px_rgba(0,0,0,0.08)] flex flex-col items-center justify-center">
            <div className="font-IvyMode-Reg rounded-full w-20 h-20 border-4 border-[#919191]/20 flex flex-col items-center justify-center font-12 text-[#919191]">
              <span className="font-20 text-black leading-none">$0</span>/$1000
            </div>
            <p className="mt-2.5 font-12">再消费1000美元即可升级</p>
          </div>
      </div>
    </div>
  )
}