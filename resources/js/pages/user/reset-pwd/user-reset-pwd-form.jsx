import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input, ConfigProvider, Tabs } from 'antd';
import GhaConfigProvider from "@/_components/GhaConfigProvider"
import mockjs from "mockjs";


export default function UserResetPwdFrom() {
  const [form] = Form.useForm();

  function onFinish(values) {

  }
  
  return (
    <div className="p-10">
      <Form
        layout="vertical"
        form={form}
        onFinish={onFinish}
        className="gha-form"
      >
        <div className="lg:w-[360px]">
          <Form.Item label="旧密码*" name="old_password" className='flex-1 lg:mr-5'>
            <Input.Password variant='underlined' placeholder="请输入您的旧密码" />
          </Form.Item>
          <Form.Item label="新密码*" name="new_password" className='flex-1 lg:mr-5'>
            <Input.Password variant='underlined' placeholder="请输入您的新密码" />
          </Form.Item>
          <Form.Item label="确认新密码*" name="confirm_new_password" className='flex-1 lg:mr-5'>
            <Input.Password variant='underlined' placeholder="请输入您的确认新密码" />
          </Form.Item>
        </div>
        <Button disabled className="gha-primary-btn !py-1 w-full lg:w-48" type="primary" htmlType="submit">保存</Button>
      </Form>
    </div>
  )
}

createRoot(document.querySelector("#user-reset-pwd-form")).render(
  <GhaConfigProvider><UserResetPwdFrom /></GhaConfigProvider>
);