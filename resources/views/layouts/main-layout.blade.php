<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        @yield("meta")
        <title>@yield('title')</title>
        <link rel="shortcut icon" href="{{ asset('favicon.ico') }}"/>
        <script src="{{ asset('vendors/js/da-event.min.js') }}"></script>
        <script>
            window.__Global_Subject__ = new DaEvent();
        </script>
        <link rel="stylesheet" href="{{ config('app.iconfont_url') }}">
        @vite("resources/css/app.scss")
        @yield('styles')
        @include('partials.layout-assign')
    </head>
    <body>
        <div class="app-layout-wrap {{ $layoutRootClassName ?? '' }}">
            @include('partials.header.header')
            <div class="relative">
                @yield('content')
                @include('partials.back-top-nav')
            </div>
            @if(!($hideFooterBrands ?? false))
                @include('partials.brands')
            @endif
            
            @include('partials.footer')
        </div>
        <script src="{{ asset('vendors/js/jquery-2.2.4.min.js') }}"></script>
        <script src="{{ asset('vendors/js/rxjs.umd.min.js') }}"></script>
        @vite("resources/js/app.js")
        @viteReactRefresh
        @vite('resources/js/global/react-modal.jsx')
        @yield("scripts")
    </body>
</html>
