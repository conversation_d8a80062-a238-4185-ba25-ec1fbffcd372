@extends('layouts.main-layout')
@section("styles")
    <link rel="stylesheet" href="{{ asset('vendors/swiper/swiper-bundle.min.css') }}">
@endsection

@section('content')
  <div class="destination-page-wrap">
    <div class="px-4 lg:px-0 lg:w-[900px] lg:mx-auto py-24 text-center">
      <h1 class="font-Jost-SemiBold font-36 font-medium text-primary">探索GHA全球酒店</h1>
      <h5 class="font-16 mt-2">GHA探索之旅是为现代全球探险家打造的酒店忠诚度计划。 我们相信最好的旅行体验来自对目的地的真正沉浸，我们在 40 个领域提供认可 独立豪华酒店品牌 – 均具有自己独特的个性 – 超过 800多 家酒店遍布全球。</h5>
      <a href="" class="gha-primary-btn w-48 mt-8">查看酒店列表</a>
    </div>
    <div class="bg-[#f1f1f1]/60 pt-24 pb-20">
      <div class="g-main-content">
        <div class="flex flex-col xl:flex-row items-stretch min-w-0">
          <div class="xl:mr-8 xl:w-96 flex-shrink-0 text-center xl:text-left">
            <h2 class="font-24 font-medium text-primary">探索新酒店</h2>
            <p class="font-16 mt-2 leading-loose">
              GHA探索之旅系列品牌目前在100个国家/地区拥有800多家酒店和度假酒店。<br/>
              该忠诚计划由40个品牌组成，并且仍在不断壮大，将继续在全球众多热门目的地为您提供各种卓越非凡的酒旅服务。 <br/>
              欢迎探索以下最新地点的GHA探索之旅酒店，畅享无限精彩。
            </p>
          </div>
          <div class="swiper-el flex-1 md:overflow-hidden min-w-0 relative md:px-16 mt-12 xl:mt-0">
            <div class="gha-swiper-button gha-swiper-button-prev hidden md:flex"><i class="iconfont icon-a-Arrow-Left"></i></div>
            <div class="gha-swiper-button gha-swiper-button-next hidden md:flex"><i class="iconfont icon-a-Arrow-Right"></i></div>
            <div class="swiper-pagination md:hidden"></div>
            <div class="swiper-container overflow-hidden">
              <div class="swiper-wrapper">
                @foreach($hotels as $item)
                <div class="swiper-slide p-1">
                  <div class="hotel-item hotel-item-large">
                    <div class="cover-wrap">
                      <div class="logo-wrap">
                        <img src="{{$item['images'][0]['url']??''}}" alt="" />
                      </div>
                    </div>
                    <div class="tag-wrap">
                      <p>签名奢侈品</p>
                    </div>
                    <div class="info-wrap">
                      <h5 class="brand">{{$item['brand_code_cn']}}</h5>
                      <h2 class="title">{{$item['hotel_name']}}</h2>
                      <h5 class="local">{{$item['address']}}</h5>
                      <div class="spt-line"></div>
                      <div class="price-wrap">
                        <div class="protrude">
                          <p>会员价低至</p>
                          <p class="price-num">CNY1,249</p>
                        </div>
                        <div class="">
                          <p>会员价低至</p>
                          <p class="price-num">CNY1,249</p>
                        </div>
                      </div>
                      <div class="action-wrap">
                        <a href="" class="gha-primary-btn !py-1">立即预订</a>
                        <a href="" class="gha-btn !py-1">酒店详情</a>
                      </div>
                    </div>
                  </div>
                </div>
                @endforeach
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="py-24">
      <div class="g-main-content">
        <div class="flex flex-row flex-wrap -mx-3 items-stretch">
          @foreach($continent as $item)
          <div class="w-full md:w-1/2 lg:w-1/3 px-3 mt-6">
            <div class="rounded-2xl border border-[#eeeeee] overflow-hidden">
              <div class="w-full pb-[52%] bg-cover bg-no-repeat bg-center gha-bg-test" style="background-image: url({{ $item['image'] }})" ></div>
              <div class="px-4 py-6">
                <h3 class="font-16">{{$item['name']}}</h3>
                <p class="font-12 mt-2 min-h-[18px]">{{$item['description']}}</p>
                <a href="" class="mt-4 gha-primary-btn !py-1 w-full">探索酒店</a>
              </div>
            </div>
          </div>
          @endforeach
          <div class="w-full md:w-1/2 lg:w-1/3 px-3 mt-6">
            <div class="rounded-2xl border border-[#eeeeee] overflow-hidden relative">
              <div class="absolute text-white top-0 left-0 right-0 bottom-0 bg-white bg-no-repeat bg-center bg-cover" style="background-image: url({{ Vite::asset('resources/images/destination/more-bg.png') }})">
                <h2 class="absolute top-8 left-8 font-22">GHA探索之旅<br/>目的地</h2>
                <a href="" class="absolute bottom-8 right-4 font-14 flex items-center">查看全部<i class="iconfont icon-youjiantou font-24 ml-1.5"></i></a>
              </div>
              <div class="">
                <div class="w-full pb-[52%] bg-cover bg-no-repeat bg-center gha-bg-test opacity-0"></div>
                <div class="px-4 py-6 opacity-0">
                  <h3 class="font-16">非洲</h3>
                  <p class="font-12 mt-2 min-h-[18px]">从宏伟的泰姬陵到繁华的香港大都市，潜入这个迷人的地区，领略异国风情。</p>
                  <a href="" class="mt-4 gha-primary-btn !py-1 w-full">探索酒店</a>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection


@section('scripts')
  <script src="{{ asset('vendors/swiper/swiper-bundle.min.js') }}"></script>
  @vite("resources/js/pages/destination/destination.js")
@endsection
