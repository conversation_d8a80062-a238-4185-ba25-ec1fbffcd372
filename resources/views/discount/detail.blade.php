@extends('layouts.main-layout')
@section("styles")
    <link rel="stylesheet" href="{{ asset('vendors/swiper/swiper-bundle.min.css') }}">
@endsection

@section('content')
  <div class="discount-page-wrap">
    <div class="gha-gradient-top relative">
      <div class="pt-16 pb-14">
        <div class="g-main-content-sm">
          <div class="flex flex-row items-center justify-between">
            <h1 class="text-white font-36 font-semibold">住宿优惠</h1>
            <a href="/discount/search" class="text-white font-14 underline">
              <i class="iconfont icon-left"></i>返回查看全部住宿优惠
            </a>
          </div>
        </div>
      </div>

    </div>
    @php
        $anchors = ["住宿优惠", "优惠细则", "条款细则"];
    @endphp
    <div class="-mt-[39px] relative hidden lg:block">
      <div class="fixed-bar">
        <div class="fixed-sentinel-top"></div>
        <div class="fixed-holder h-[78px]"></div>
        <div class="bar-el">
          <div class="g-main-content-sm">
            <div class="inner flex flex-row bg-white shadow-md border-[#E0E0E0]/20 rounded-xl">
              @foreach($anchors as $i => $item)
                <div class="font-16 px-8 relative after:content-[''] last:after:content-none after:absolute after:w-0.5 after:h-4 after:bg-[#919191]/20 after:block after:right-0 after:top-1/2 after:-translate-y-1/2">
                  <a href="#anchor{{$i}}" class="anchor-item !leading-[78px] h-[78px] inline-block {{ $i === 0 ? 'active' : '' }}">{{ $item }}</a>
                </div>
              @endforeach
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="-mt-[39px] relative lg:hidden">
      <div class="fixed-bar-m">
        <div class="fixed-sentinel-top"></div>
        <div class="fixed-holder h-[56px]"></div>
        <div class="bar-el">
          <div class="flex flex-row bg-white shadow-md border-[#E0E0E0]/20 rounded-t-xl">
            @foreach($anchors as $i => $item)
              <div class="flex-1 lg:flex-none font-14 px-8 relative after:content-[''] last:after:content-none after:absolute after:w-0.5 after:h-4 after:bg-[#919191]/20 after:block after:right-0 after:top-1/2 after:-translate-y-1/2">
                <a href="#anchor{{$i}}" class="anchor-item !leading-[56px] h-[56px] inline-block {{ $i === 0 ? 'active' : '' }}">{{ $item }}</a>
              </div>
            @endforeach
          </div>
        </div>
      </div>

    </div>
    <div class="py-6 md:py-12 relative border-b border-[#919191]/20">
      <div class="absolute -top-[145px] lg:-top-[167px]" id="anchor0"></div>
      <div class="g-main-content-sm">
        <div class="flex flex-col md:flex-row justify-stretch rounded-xl shadow-md shadow-black/5 border border-[#ebebeb]/20 bg-white overflow-hidden">
          <div class="flex-1">
            <div class="w-full pb-[58%] bg-slate-300 relative gha-bg-test bg-cover bg-center bg-no-repeat"></div>
            <div class="bg-primary text-white text-center py-1.5 font-14">住宿与餐饮</div>
          </div>
          <div class="flex-1">
            <div class="p-4 md:p-8 flex flex-col h-full">
              <h4 class="font-16">大连凯宾斯基酒店</h4>
              <a class="underline text-[#999] font-12">查看酒店详情</a>
              <h3 class="font-22 font-medium mt-4">冬季节日季</h3>
              <p class="font-18 font-IvyMode-Reg">最多节省10%</p>
              <div class="flex-1"></div>
              <div class="text-right">
                <a id="orderNowBtn" href="javascript:;" class="gha-primary-btn !px-8">立即预订</a>
              </div>
            </div>

          </div>
        </div>
        <div class="mt-4 md:mt-12">
          <div class="flex flex-col md:flex-row justify-between">
            <div class="flex flex-row mt-4 md:mt-0">
              <i class="iconfont icon-Location font-24 !leading-6"></i>
              <div class="font-14 !leading-6">
                <p>中山区解放路 92 号</p>
                <p>中国，大连</p>
                <a href="javascript:;" class="text-primary underline">查看地图</a>
              </div>
            </div>
            <div class="flex flex-row mt-4 md:mt-0">
              <i class="iconfont icon-Calendar font-24 !leading-6"></i>
              <div class="font-14 !leading-6">
                <p>预订期限至 </p>
                <p>2024年12月31日</p>
              </div>
            </div>
            <div class="flex flex-row mt-4 md:mt-0">
              <i class="iconfont icon-Room font-24 !leading-6"></i>
              <div class="font-14 !leading-6">
                <p>住宿期限：</p>
                <p>2024年1月25日 - 2024年12月31日</p>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
    <div class="py-6 md:py-12 border-b border-[#919191]/20 relative">
      <div class="absolute -top-[145px] lg:-top-[167px]" id="anchor1"></div>
      <div class="g-main-content-sm">
        <h2 class="font-22 font-semibold">优惠细则</h2>
        <div class="mt-4 font-14">
          <p>值此新年即将来临之际，我们向您致以最热烈的节日祝福。 远处传来的欢庆声预示着节日聚会的开始。 大连凯宾斯基酒店很高兴邀请您加入我们的节日之旅，在那里您可以发现惊喜并体验丰富多彩的节日的奇妙。 </p>
          <p class="mt-2">
            提前预订住宿即可享受最优惠价格（含早餐）高达 35% 的折扣。 <br/>
            预订至少 45 间客房 抵达前几天预订，即可享受最优惠价格 25% 的折扣。 <br/>
            至少在抵达前 90 天预订住宿，即可享受最优惠价格 的折扣。
          </p>
          <p class="mt-2">
            优惠包括：<br/>
            在 Café Lazare 吃早餐<br/>
            免费 Wi-Fi 连接
          </p>
        </div>
      </div>
    </div>
    <div class="py-6 md:py-12 relative">
      <div class="absolute -top-[145px] lg:-top-[167px]" id="anchor2"></div>
      <div class="g-main-content-sm">
        <div class="flex flex-row items-center justify-between">
          <h2 class="font-22 font-semibold">条件与条款</h2>
          <a id="togglePolicy" href="javascript:;" class="flex items-center justify-center rounded-full w-10 h-10 shadow-md shadow-black/10 border border-[#CBCBCB]/15">
            <i class="iconfont icon-down text-[#666] font-24 "></i>
          </a>
        </div>
        <div id="togglePolicyContent" class="font-14 mt-4 hidden">
          值此新年即将来临之际，我们向您致以最热烈的节日祝福。 远处传来的欢庆声预示着节日聚会的开始。 大连凯宾斯基酒店很高兴邀请您加入我们的节日之旅，在那里您可以发现惊喜并体验丰富多彩的节日的奇妙。
        </div>
      </div>
    </div>
    <div class="discount-detail-content"></div>
  </div>
@endsection


@section('scripts')
  <script src="{{ asset('vendors/swiper/swiper-bundle.min.js') }}"></script>
  @vite("resources/js/pages/discount/detail/discount-detail.js")
  @viteReactRefresh
  @vite("resources/js/pages/discount/detail/discount-detail.jsx")
@endsection
