<div class="hotel-item hotel-item-large hotel-room-item" data-id="{{ $hotel['id'] }}">
    <div class="cover-wrap">
        <div class="cover-swiper ignore-opacity">
            <div class="gha-swiper-button gha-swiper-button-prev"><i class="iconfont icon-a-Arrow-Left"></i></div>
            <div class="gha-swiper-button gha-swiper-button-next"><i class="iconfont icon-a-Arrow-Right"></i></div>
            <div class="swiper-pagination"></div>
            <div class="swiper-pagination-nums"><span class="cur">1</span>/5</div>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    @foreach(range(1, 5) as $index)
                    <div class="swiper-slide">
                        <img class="w-full h-full" src="https://storage.ghadiscovery.com/cdn-cgi/image/width=658,height=428,f=auto,g=auto,fit=cover/img/images/2/0/1/3/663102-1-eng-GB/f266d2ff8ee1-Avani_Gaborone_Resort_and_Casino_Avani_Superior-Room_3.jpg" alt="">
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    <div class="tag-wrap">
        <p>签名奢侈品</p>
    </div>
    <div class="info-wrap">
    <h5 class="brand">{{ $hotel["brand"] }}</h5>
    <h2 class="title">{{ $hotel["name"] }}</h2>
    <h5 class="room-area">
        <i class="iconfont icon-mianji"></i>
        48m²
    </h5>
    <h5 class="local">{{ $hotel["country"] }},{{ $hotel["city"] }}</h5>
    <div class="spt-line"></div>
    <div class="price-wrap">
        <div class="protrude">
        <p>会员价低至</p>
        <p class="price-num">CNY{{ $hotel['price'] }}</p>
        </div>
        <div class="">
        <p>会员价低至</p>
        <p class="price-num">CNY{{ $hotel['price_membership'] }}</p>
        </div>
    </div>
    <div class="action-wrap">
        <a href="" class="gha-primary-btn !py-1">立即预订</a>
    </div>
    </div>
</div>