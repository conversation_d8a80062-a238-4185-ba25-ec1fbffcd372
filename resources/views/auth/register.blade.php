@extends('layouts.main-layout')

@section('content')
    <div class="register-page-wrap">
      <div class="top-sec"></div>
      <div class="page-main-wrap">
        <div class="g-main-content">
          <h1>欢迎加入GHA全球会员计划</h1>
          <div class="page-content-wrap">
            <div class="form-wrap"></div>
            <div class="extra-wrap">
              <div class="extra-list">
                <div class="">
                  <h4>GHA代表着40个品牌<br/>800多家酒店遍布100个国家<br/>服务会员25万</h4>
                </div>
                <div class="">
                  <h2>高额优惠的住宿体验</h2>
                  <p>免费注册GHA会员忠诚计划，即可享受全球多地的酒店及度假村的800多项超值优惠及体验</p>
                </div>
                <div class="">
                  <h2>额外获得奖励金回馈</h2>
                  <p>会员消费即可获最高7%的高额DISCOVERY奖励金回馈货币，用于下一次的灵活消费</p>
                </div>
                <div class="">
                  <h2>所心所欲的体验探索</h2>
                  <p>会员无需入住，即可畅享本地生活优惠及特色体验优惠，无需远行也能制造难忘的旅行瞬间</p>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
@endsection


@section('scripts')
    <script>
      window.__ServerVars__ = {
        activeUri: "{{ route('auth.active') }}",
        loginUri: "{{ route('auth.login') }}",
        countryList: @json($country)
      }
    </script>
    @viteReactRefresh
    @vite("resources/js/pages/auth/register-form/register-form.jsx")
@endsection
