<?php

namespace Tests\Unit;

use App\Services\GxpService;
use App\Services\GxpHttpService;
use App\DTOs\Gxp\GuestTokenDTO;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Mockery;

class GxpTokenCacheTest extends TestCase
{
    protected $gxpService;
    protected $mockHttpService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockHttpService = Mockery::mock(GxpHttpService::class);
        $this->gxpService = new GxpService($this->mockHttpService);
    }

    public function test_token_is_cached_after_first_request()
    {
        // 模拟第一次获取token
        $tokenDto = new GuestTokenDTO(
            'test_token_123',
            'Bearer',
            3600,
            null,
            'guest',
            null,
            null,
            null
        );

        $this->mockHttpService
            ->shouldReceive('post')
            ->once()
            ->andReturn(['code' => 200, 'data' => $tokenDto->toArray()]);

        $this->mockHttpService
            ->shouldReceive('get')
            ->once()
            ->andReturn(['code' => 200, 'data' => ['rates' => []]]);

        // 设置凭据并调用服务
        $result1 = $this->gxpService
            ->setCredentials('test_user', 'test_pass')
            ->getCurrencyExchangeRate('USD', '2025-01-01');

        // 验证缓存中有token
        $cacheKey = 'gxp_token_' . md5('test_user' . 'test_pass');
        $cachedData = Cache::get($cacheKey);

        $this->assertNotNull($cachedData);
        $this->assertEquals('test_token_123', $cachedData['token']);
    }

    public function test_cached_token_is_reused()
    {
        // 预先设置缓存
        $cacheKey = 'gxp_token_' . md5('test_user' . 'test_pass');
        Cache::put($cacheKey, [
            'token' => 'cached_token_456',
            'expires_at' => time() + 3600,
            'token_type' => 'Bearer'
        ], 3300); // 55分钟

        // 模拟汇率API调用
        $this->mockHttpService
            ->shouldReceive('get')
            ->once()
            ->with('ddollars/exchange-rate', Mockery::any(), 'cached_token_456', 'rotana')
            ->andReturn(['code' => 200, 'data' => ['rates' => []]]);

        // 不应该调用token获取API
        $this->mockHttpService
            ->shouldNotReceive('post');

        $result = $this->gxpService
            ->setCredentials('test_user', 'test_pass')
            ->getCurrencyExchangeRate('USD', '2025-01-01');

        $this->assertIsArray($result);
    }

    public function test_expired_token_is_refreshed()
    {
        // 设置即将过期的缓存token
        $cacheKey = 'gxp_token_' . md5('test_user' . 'test_pass');
        Cache::put($cacheKey, [
            'token' => 'expired_token',
            'expires_at' => time() + 200, // 3分钟后过期，小于5分钟阈值
            'token_type' => 'Bearer'
        ], 200);

        // 模拟获取新token
        $newTokenDto = new GuestTokenDTO(
            'new_token_789',
            'Bearer',
            3600,
            null,
            'guest',
            null,
            null,
            null
        );

        $this->mockHttpService
            ->shouldReceive('post')
            ->once()
            ->andReturn(['code' => 200, 'data' => $newTokenDto->toArray()]);

        $this->mockHttpService
            ->shouldReceive('get')
            ->once()
            ->with('ddollars/exchange-rate', Mockery::any(), 'new_token_789', 'rotana')
            ->andReturn(['code' => 200, 'data' => ['rates' => []]]);

        $result = $this->gxpService->getCurrencyExchangeRate(
            'test_user',
            'test_pass',
            'USD',
            '2025-01-01'
        );

        // 验证缓存已更新
        $updatedCache = Cache::get($cacheKey);
        $this->assertEquals('new_token_789', $updatedCache['token']);
    }

    public function test_cache_key_generation()
    {
        $reflection = new \ReflectionClass($this->gxpService);
        $method = $reflection->getMethod('getOrRefreshToken');
        $method->setAccessible(true);

        // 不同用户应该有不同的缓存键
        $user1Key = 'gxp_token_' . md5('user1' . 'pass1');
        $user2Key = 'gxp_token_' . md5('user2' . 'pass2');

        $this->assertNotEquals($user1Key, $user2Key);
    }

    public function test_cache_info_method()
    {
        // 设置测试缓存
        $cacheKey = 'gxp_token_' . md5('test_user' . 'test_pass');
        $expiresAt = time() + 1800; // 30分钟后过期

        Cache::put($cacheKey, [
            'token' => 'info_test_token',
            'expires_at' => $expiresAt,
            'token_type' => 'Bearer'
        ], 1500);

        $info = $this->gxpService->getTokenCacheInfo('test_user', 'test_pass');

        $this->assertNotNull($info);
        $this->assertTrue($info['has_token']);
        $this->assertEquals($expiresAt, $info['expires_at']);
        $this->assertEquals('Bearer', $info['token_type']);
        $this->assertGreaterThan(0, $info['expires_in_seconds']);
    }

    public function test_clear_token_cache()
    {
        // 设置测试缓存
        $cacheKey = 'gxp_token_' . md5('test_user' . 'test_pass');
        Cache::put($cacheKey, [
            'token' => 'to_be_cleared',
            'expires_at' => time() + 3600,
            'token_type' => 'Bearer'
        ], 3300);

        // 验证缓存存在
        $this->assertNotNull(Cache::get($cacheKey));

        // 清除缓存
        $result = $this->gxpService->clearTokenCache('test_user', 'test_pass');

        $this->assertTrue($result);
        $this->assertNull(Cache::get($cacheKey));
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
