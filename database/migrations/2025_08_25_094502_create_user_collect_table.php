<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserCollectTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_collect', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('type')->nullable()->comment('收藏类型  hotel 酒店 activity活动');
            $table->string('collect')->nullable()->comment('收藏id');
            $table->integer('user_id')->nullable()->comment('用户');
            $table->string('status')->nullable()->comment('1:正常，2:禁用');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_collect');
    }
}
