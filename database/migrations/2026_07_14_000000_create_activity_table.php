<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity', function (Blueprint $table) {
            $table->id();
            $table->integer('activity_type')->nullable()->comment('活动类型');
            $table->string('name_en')->nullable()->comment('活动英文名称');
            $table->string('name')->nullable()->comment('活动名称');
            $table->string('address')->nullable()->comment('地理位置');
            $table->integer('is_show')->nullable()->comment('是否展示文字1:正常，0:禁用');
            $table->text('web_desc')->nullable()->comment('网页描述');
            $table->text('key_words')->nullable()->comment('关键词');
            $table->string('page_title')->nullable()->comment('页面标题');
            $table->text('description')->nullable()->comment('描述');
            $table->string('activity_link')->nullable()->comment('activityLink');
            $table->string('rate')->nullable()->comment('特殊价格码rate');
            $table->string('access_code')->nullable()->comment('AccessCode');
            $table->integer('status')->nullable()->comment('状态1:上线，0:下线');
            $table->integer('line_type')->nullable()->comment('链接类型');
            $table->string('url')->nullable()->comment('链接地址');
            $table->string('button_text')->nullable()->comment('按钮文字');
            $table->dateTime('date')->nullable()->comment('发布时间');
            $table->string('img_url')->nullable()->comment('简体图片');
            $table->string('img_url_f')->nullable()->comment('繁体图片');
            $table->text('content')->nullable()->comment('pc版活动内容');
            $table->integer('order')->nullable()->comment('排序');
            $table->integer('user_id')->nullable()->comment('操作人');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity');
    }
};
