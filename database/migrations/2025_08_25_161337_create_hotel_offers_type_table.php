<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateHotelOffersTypeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hotel_offers_type', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable()->comment('优惠名称');
            $table->string('name_en')->nullable()->comment('优惠名称英文');
            $table->string('image')->nullable()->comment('图片');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hotel_offers_type');
    }
}
