<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hotel_dining', function (Blueprint $table) {
            $table->id(); // 自增主键ID
            $table->string('brand_code')->comment('品牌代码');
            $table->string('hotel_code')->comment('酒店代码');
            $table->string('name')->comment('餐厅名称');
            $table->string('name_en')->comment('餐厅名称en');
            $table->string('sub_title')->comment('餐厅子标题');
            $table->string('sub_title_en')->comment('餐厅子标题en');
            $table->json('images')->nullable()->comment('餐厅照片');
            $table->string('cuisine')->comment('餐厅特色');
            $table->string('website')->comment('餐厅官网');
            $table->string('phone')->comment('餐厅联系电话');
            $table->string('reservation_url')->comment('餐厅预约链接');
            $table->string('menu_1_url')->comment('菜单图片1');
            $table->string('menu_2_url')->comment('菜单图片2');
            $table->string('start_time')->comment('营业开始时间');
            $table->string('end_time')->comment('营业开始时间');
            $table->integer('dining_id')->comment('餐厅id');
            $table->string('version_no')->nullable()->comment('同步/跟踪更新记录的版本号');
            $table->timestamps(); // 创建和更新时间
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_dining');

    }
};
