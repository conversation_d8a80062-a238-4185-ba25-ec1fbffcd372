<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateConfigsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('configs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('key')->nullable()->comment('配置键名');
            $table->text('value')->nullable()->comment('配置值');
            $table->string('type')->nullable()->comment('配置类型');
            $table->string('group')->nullable()->comment('配置分组');
            $table->text('options')->nullable()->comment('选择框选项');
            $table->string('description')->nullable()->comment('配置描述');
            $table->integer('sort')->default('0')->nullable()->comment('排序');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('configs');
    }
}
