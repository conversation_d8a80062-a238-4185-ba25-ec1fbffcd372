<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('city', function (Blueprint $table) {
            $table->id();
            $table->integer('city_code')->nullable()->comment('城市code');
            $table->string('country_code')->nullable()->comment('国家code');
            $table->string('city_name')->nullable()->comment('城市名');
            $table->string('city_name_en')->nullable()->comment('城市英文名');
            $table->integer('status')->nullable()->default(1)->comment('1在线2下线');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banner');
    }
};
