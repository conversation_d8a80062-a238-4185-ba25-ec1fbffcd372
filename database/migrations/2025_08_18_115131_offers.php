<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('hotel_offers', function (Blueprint $table) {
            $table->id(); // 自增主键ID
            $table->string('title_en')->comment('优惠标题');; // 优惠标题
            $table->string('url')->unique()->comment('跳转链接'); // 优惠链接，唯一
            $table->unsignedBigInteger('start_date')->comment('优惠开始时间'); // 优惠开始时间（UNIX时间戳）
            $table->unsignedBigInteger('end_date')->comment('优惠结束时间'); // 优惠结束时间（UNIX时间戳）
            $table->unsignedBigInteger('stay_start_date')->comment('入住开始日期'); // 入住开始日期（UNIX时间戳）
            $table->unsignedBigInteger('stay_end_date')->comment('入住结束日期'); // 入住结束日期（UNIX时间戳）
            $table->string('hotel_name_en')->comment('酒店名称'); // 酒店名称
            $table->string('rate_code1')->comment('房价代码1')->nullable(); // 房价代码1
            $table->string('rate_code2')->nullable()->comment('房价代码2'); // 房价代码2
            $table->string('rate_code3')->nullable()->comment('房价代码3'); // 房价代码3
            $table->string('rate_code4')->nullable()->comment('房价代码4'); // 房价代码4
            $table->string('rate_checked')->nullable()->comment('房价核对状态'); // 房价核对日期或状态
            $table->boolean('featured')->default(false)->comment('是否推荐'); // 是否推荐（true/false）
            $table->string('offer_types')->nullable()->comment('优惠类型'); // 优惠类型，例如：提前预订、度假套餐等
            $table->string('promo_code')->nullable()->comment('优惠码'); // 优惠码
            $table->text('description')->nullable()->comment('优惠描述'); // 优惠描述
            $table->text('offer_includes')->nullable()->comment('优惠包含'); // 优惠包含内容，例如早餐、折扣等
            $table->text('terms_conditions')->nullable()->comment('条款与条件'); // 条款与条件
            $table->text('taxes')->nullable()->comment('税费说明'); // 税费说明
            $table->timestamps(); // 创建和更新时间
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_offers');
    }
};
