<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateBannerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('banner', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('type')->nullable()->comment('banner类型');
            $table->string('title')->nullable()->comment('标题');
            $table->string('url')->nullable()->comment('跳转地址');
            $table->integer('status')->nullable()->comment('状态1:上线，0:下线');
            $table->string('img_url')->nullable()->comment('图片地址');
            $table->text('order_num')->nullable()->comment('排序');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('banner');
    }
}
