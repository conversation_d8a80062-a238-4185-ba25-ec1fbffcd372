<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateWellnessTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wellness', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('wellness_id')->nullable()->comment('源id');
            $table->string('brand_code')->nullable()->comment('品牌码');
            $table->string('hotel_code')->nullable()->comment('酒店码');
            $table->string('name')->nullable()->comment('健康名称');
            $table->string('name_en')->nullable()->comment('健康名称英文');
            $table->json('images')->nullable()->comment('图片');
            $table->string('website')->nullable()->comment('站点');
            $table->string('phone')->nullable()->comment('联系电话');
            $table->string('reservation_url')->nullable()->comment('预约网址');
            $table->string('menu_1_url')->nullable()->comment('菜单1');
            $table->string('menu_2_url')->nullable()->comment('菜单2');
            $table->dateTime('start_time')->nullable()->comment('开始时间');
            $table->dateTime('end_time')->nullable()->comment('结束时间');
            $table->integer('version_no')->nullable()->comment('版本号');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wellness');
    }
}
