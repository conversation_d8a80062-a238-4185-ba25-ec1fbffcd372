<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('room', function (Blueprint $table) {
            $table->id();
            $table->string('hotelcode')->nullable()->comment('酒店代码');
            $table->string('brandCode')->nullable()->comment('品牌代码');
            $table->string('code')->nullable(false)->comment('房间短标识');
            $table->string('roomcode')->nullable(false)->comment('房型原始id');
            $table->string('synxis_room_id')->nullable()->comment('SynXis集成中使用的房间ID');
            $table->string('size')->nullable()->comment('房间大小的可选字段（例如“30平方米”）。当前为空');
            $table->string('room_name')->nullable()->comment('房型名称(中文)');
            $table->string('room_name_en')->nullable()->comment('房型名称(英文)');
            $table->text('room_desc')->nullable()->comment('房型描述(中文)');
            $table->text('room_desc_en')->nullable()->comment('房型描述(英文)');
            $table->text('booker_text')->nullable();
            $table->text('booker_text_en')->nullable();
            $table->string('free_wifi')->nullable()->comment('是否含免费宽带 Y: 含免费宽带');
            $table->string('bedtype')->nullable()->comment('床型代码');
            $table->string('room_class')->nullable()->comment('房型分类');
            $table->integer('max_adults')->nullable();
            $table->integer('version_no')->nullable()->comment('版本号');
            $table->integer('location_id')->nullable()->comment('地区id');
            $table->integer('max_children')->nullable();
            $table->integer('max_occupancy')->nullable();
            $table->integer('rollaway_count')->nullable();
            $table->integer('is_online')->nullable();
            $table->integer('selling_order')->nullable();
            $table->timestamp('updatetime')->nullable()->comment('更新时间');
            $table->integer('trust_online')->nullable();
            $table->text('rma_code')->nullable();
            $table->string('booker_text_lang')->nullable();
            $table->string('room_name_en_new')->nullable();
            $table->text('booker_text_en_new')->nullable();
            $table->json("extend")->comment("扩展字段");
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('room');
    }
};
