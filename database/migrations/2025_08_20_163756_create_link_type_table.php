<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLinkTypeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('link_type', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable()->comment('分类名称');
            $table->integer('status')->default('1')->nullable()->comment('状态1:上线，0:下线');
            $table->text('order_num')->nullable()->comment('排序');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('link_type');
    }
}
