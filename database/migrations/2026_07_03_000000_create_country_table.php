<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('country', function (Blueprint $table) {
            $table->id();
            $table->string('country_code')->nullable()->comment('国家code');
            $table->string('country_name')->nullable()->comment("国家名称");
            $table->string('country_name_en')->nullable()->comment("国家英文名");
            $table->string('country_name_pinyin')->nullable()->comment('国家拼音');
            $table->string('continent_name_en')->nullable()->comment('大陆名称');
            $table->text('brief_en')->nullable();
            $table->text('brief')->nullable();
            $table->text('detail_en')->nullable();
            $table->text('detail')->nullable();
            $table->text('introducing')->nullable();
            $table->text('introducing_en')->nullable();
            $table->string('image_url')->nullable();
            $table->string('status')->nullable();
            $table->string('country_url')->nullable();
            $table->string('country_link')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('country');
    }
};
