<?php

use Illuminate\Support\Facades\Route;

Route::post('brand', 'BaseDataController@brand');
Route::post('hotel', 'BaseDataController@hotel');
Route::post('room', 'BaseDataController@room');
Route::group(['prefix' => 'auth', 'namespace' => 'Auth'], function () {
    Route::post('/register', 'User@register'); //注册
    Route::post('/login', 'User@login'); //登录
    Route::post('/reset_pwd', 'User@resetPwd'); //发送找回密码邮件
    Route::post('/set_pwd', 'User@setPwd'); //重置密码
    Route::post('/active', 'User@active'); //账号激活
    Route::post('/find_member', 'User@findMember'); //找回会员号
});

Route::group(['prefix' => 'home'], function () {
    Route::get('/index', 'HotelController@index');
});

// Sabre API 路由
Route::group(['prefix' => 'sabre'], function () {
    // 酒店可用性查询
    Route::post('/availability', 'SabreController@checkAvailability');

    // 预订管理
    Route::post('/reservation', 'SabreController@createReservation');
    Route::get('/reservation', 'SabreController@getReservation');
    Route::patch('/reservation', 'SabreController@modifyReservation');
    Route::delete('/reservation', 'SabreController@cancelReservation');

    // 酒店信息
    Route::get('/hotel/{hotelId}', 'SabreController@getHotelDetails');
    Route::get('/hotel/{hotelId}/payment-methods', 'SabreController@getHotelPaymentMethods');

    // 缓存管理
    Route::delete('/cache', 'SabreController@clearCache');
});


Route::group(['prefix' => 'user', 'namespace' => 'Auth', 'middleware' => 'auth:sanctum'], function () {
    Route::post('/add_collect', 'User@addCollect')->name("user.addCollect");;
});
Route::prefix("about")->group(function () {
    //新闻动态
    Route::get('/newsList', 'AboutController@newsList')->name("about.newsList");
});

Route::prefix("offers")->group(function () {
    //优惠活动
    Route::get('/typeList', 'HotelController@offersTypeList')->name("offers.offersTypeList");
});

//需要登录的操作
Route::group(['middleware' => 'auth:sanctum'], function () {
    Route::post('/logout', 'Auth\User@logout'); //退出登录
    //个人信息
    Route::get('/user_info', 'Auth\User@userInfo');
    //修改密码
    Route::post('/up_pwd', 'Auth\User@upPwd');
});

Route::group(['prefix' => 'hotel'], function () {
    // 酒店首页
    Route::get('/home', 'HotelController@home');
    Route::get('/detail', 'HotelController@detail');
    //关键词搜索
    Route::post('/searchKey', 'HotelController@searchKey');
    //关键词搜索获取国家城市酒店
    Route::get('/searchObscure', 'HotelController@searchObscure');
    //热门搜索
    Route::get('/searchHot', 'HotelController@searchHot');
    //搜索活动
    Route::get('/searchActivity', 'HotelController@searchActivity');
    //预定酒店搜索
    Route::get('/searchHotels', 'HotelController@searchHotels');
});


Route::group(['prefix' => 'brand'], function () {
    // 品牌列表
    Route::get('/brand_list', 'BrandController@lists');
    //品牌详情
    Route::get('/brand_info', 'BrandController@detail');
});

Route::group(['prefix' => 'banner'], function () {
    // banner列表
    Route::get('/banner_list', 'BannerController@lists');
});

Route::group(['prefix' => 'activity'], function () {
    // 活动
    Route::get('/activity_many_list', 'ActivityController@listsManyType');
    Route::get('/activity_list', 'ActivityController@lists');
    Route::get('/activity_info', 'ActivityController@detail');
});

//基础配置
Route::group(['prefix' => 'config'], function () {
    //国家
    Route::get('/country_list', 'ConfigController@countryList');
    //城市
    Route::get('/city_list', 'ConfigController@cityList');
    //酒店配置
    Route::get('/hotel_config', 'ConfigController@hotelConfig');
    //喜好设置
    Route::get('/interests_config', 'ConfigController@interestsConfig');
});

//酒店配置
Route::post('updateHotelConfig', 'BaseDataController@hotelConfig');
//酒店品牌同步
Route::post('updateBrandConfig', 'BaseDataController@brandConfig');

// GXP API 路由
Route::group(['prefix' => 'gxp'], function () {
    // 访客令牌（无需认证）
    Route::post('/auth/token', 'GxpController@getGuestToken');
    
    Route::get('/ddollars/exchange-rate', 'GxpController@getCurrencyConversionRate');

    // 预订管理
    Route::post('/reservations-repo', 'GxpController@reserveRepoPosting');
    Route::get('/reservations-repo', 'GxpController@getMyBookings');
});
