<?php
return [
    'base_url' => env('GHA_BASE_URL', 'https://escp.stage.ghaloyalty.com/brand-api/v2/'),
    'base_url_v3' => env('GHA_BASE_URL', 'https://escp.stage.ghaloyalty.com/brand-api/v3/'),
    'api_v1' => env('GHA_API_V3', 'https://gxp.stage.ghaloyalty.com/api/v1/'),
    'api_v2' => env('GHA_API_V3', 'https://oscp.stage.ghaloyalty.com/api/v2/'),
    'api_v3' => env('GHA_API_V3', 'https://escp.stage.ghaloyalty.com/api/v3/'),
    'base_locale' => env('GHA_LOCALE', 'ZH'),
    'token' => env('GHA_TOKEN', 'N2l0ZERiUzZYUHczYVlYZk5zbGcyWm1QWm1oRjdNYkhOMXZWbzIyUEZ1UGlMYlZVNGtlZWpWZ3A5NUo1SkZubA'),
    'app_key_china' => env('GHA_APP_KEY_CHINA', ''),
    'app_key_rotana' => env('GHA_APP_KEY_ROTANA', ''),

    // GXP服务配置
    'gxp' => [
        'username' => env('GHA_GXP_USERNAME', ''),
        'password' => env('GHA_GXP_PASSWORD', ''),
        'auto_initialize' => env('GHA_GXP_AUTO_INITIALIZE', true),
    ],
];
