<?php

namespace App\Services;

use App\DTOs\Sabre\AvailabilityRequestDTO;
use App\DTOs\RoomAvailabilityResponseDTO;
use App\Exceptions\SabreApiException;
use App\Traits\SabreLoggingTrait;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SabreAvailabilityService
{
    use SabreLoggingTrait;

    protected SabreService $sabreService;
    protected SabreDataTransformer $dataTransformer;

    public function __construct(SabreService $sabreService, SabreDataTransformer $dataTransformer)
    {
        $this->sabreService = $sabreService;
        $this->dataTransformer = $dataTransformer;
    }

    /**
     * 查询酒店可用性（会员和非会员价格，无促销）
     */
    public function checkMemberAndNonMemberRates(array $params): array
    {
        $request = new AvailabilityRequestDTO(
            primaryChannel: $params['primaryChannel'] ?? config('sabre.defaults.primary_channel'),
            secondaryChannel: $params['secondaryChannel'] ?? config('sabre.defaults.secondary_channel'),
            chainId: $params['chainId'] ?? config('sabre.defaults.chain_id'),
            hotelId: $params['hotelId'],
            startDate: $params['startDate'],
            endDate: $params['endDate'],
            numRooms: $params['numRooms'] ?? 1,
            adults: $params['adults'] ?? 1,
            children: $params['children'] ?? null,
            childrenAge: $params['childrenAge'] ?? null,
            loyaltyProgram: $params['loyaltyProgram'] ?? config('sabre.defaults.loyalty_program'),
            loyaltyLevel: $params['loyaltyLevel'] ?? null,
            content: $params['content'] ?? 'full'
        );

        return $this->checkAvailabilityWithCache($request, $params);
    }

    /**
     * 查询酒店可用性并返回标准格式（会员和非会员价格，无促销）
     */
    public function checkMemberAndNonMemberRatesStandard(array $params): RoomAvailabilityResponseDTO
    {
        $sabreResponse = $this->checkMemberAndNonMemberRates($params);
        return $this->dataTransformer->transformAvailabilityResponse($sabreResponse);
    }

    /**
     * 查询酒店可用性（会员和非会员价格，包含促销）
     */
    public function checkMemberAndNonMemberRatesWithPromo(array $params): array
    {
        $request = new AvailabilityRequestDTO(
            primaryChannel: $params['primaryChannel'] ?? config('sabre.defaults.primary_channel'),
            secondaryChannel: $params['secondaryChannel'] ?? config('sabre.defaults.secondary_channel'),
            chainId: $params['chainId'] ?? config('sabre.defaults.chain_id'),
            hotelId: $params['hotelId'],
            startDate: $params['startDate'],
            endDate: $params['endDate'],
            numRooms: $params['numRooms'] ?? 1,
            adults: $params['adults'] ?? 1,
            children: $params['children'] ?? null,
            childrenAge: $params['childrenAge'] ?? null,
            loyaltyProgram: $params['loyaltyProgram'] ?? config('sabre.defaults.loyalty_program'),
            loyaltyLevel: $params['loyaltyLevel'] ?? null,
            content: $params['content'] ?? 'full',
            accessType: $params['accessType'] ?? 'Promotion',
            accessCode: $params['accessCode'] ?? null
        );

        return $this->checkAvailabilityWithCache($request, $params);
    }

    /**
     * 查询酒店可用性并返回标准格式（会员和非会员价格，包含促销）
     */
    public function checkMemberAndNonMemberRatesWithPromoStandard(array $params): RoomAvailabilityResponseDTO
    {
        $sabreResponse = $this->checkMemberAndNonMemberRatesWithPromo($params);
        return $this->dataTransformer->transformAvailabilityResponse($sabreResponse);
    }

    /**
     * 查询酒店可用性（仅促销价格）
     */
    public function checkPromoRateOnly(array $params): array
    {
        $request = new AvailabilityRequestDTO(
            primaryChannel: $params['primaryChannel'] ?? config('sabre.defaults.primary_channel'),
            secondaryChannel: $params['secondaryChannel'] ?? config('sabre.defaults.secondary_channel'),
            chainId: $params['chainId'] ?? config('sabre.defaults.chain_id'),
            hotelId: $params['hotelId'],
            startDate: $params['startDate'],
            endDate: $params['endDate'],
            numRooms: $params['numRooms'] ?? 1,
            adults: $params['adults'] ?? 1,
            children: $params['children'] ?? null,
            childrenAge: $params['childrenAge'] ?? null,
            loyaltyProgram: $params['loyaltyProgram'] ?? config('sabre.defaults.loyalty_program'),
            loyaltyLevel: $params['loyaltyLevel'] ?? null,
            content: $params['content'] ?? 'full',
            onlyCheckRequested: true,
            accessType: $params['accessType'] ?? 'Promotion',
            accessCode: $params['accessCode'] ?? null
        );

        return $this->checkAvailabilityWithCache($request, $params);
    }

    /**
     * 查询酒店可用性并返回标准格式（仅促销价格）
     */
    public function checkPromoRateOnlyStandard(array $params): RoomAvailabilityResponseDTO
    {
        $sabreResponse = $this->checkPromoRateOnly($params);
        return $this->dataTransformer->transformAvailabilityResponse($sabreResponse);
    }

    /**
     * 查询多房间可用性
     */
    public function checkMultiRoomAvailability(array $params): array
    {
        $request = new AvailabilityRequestDTO(
            primaryChannel: $params['primaryChannel'] ?? config('sabre.defaults.primary_channel'),
            secondaryChannel: $params['secondaryChannel'] ?? config('sabre.defaults.secondary_channel'),
            chainId: $params['chainId'] ?? config('sabre.defaults.chain_id'),
            hotelId: $params['hotelId'],
            startDate: $params['startDate'],
            endDate: $params['endDate'],
            numRooms: $params['numRooms'] ?? 2,
            adults: $params['adults'] ?? 2,
            children: $params['children'] ?? null,
            childrenAge: $params['childrenAge'] ?? null,
            loyaltyProgram: $params['loyaltyProgram'] ?? config('sabre.defaults.loyalty_program'),
            loyaltyLevel: $params['loyaltyLevel'] ?? null,
            content: $params['content'] ?? 'full',
            accessType: $params['accessType'] ?? null,
            accessCode: $params['accessCode'] ?? null
        );

        return $this->checkAvailabilityWithCache($request, $params);
    }

    /**
     * 查询多房间可用性并返回标准格式
     */
    public function checkMultiRoomAvailabilityStandard(array $params): RoomAvailabilityResponseDTO
    {
        $sabreResponse = $this->checkMultiRoomAvailability($params);
        return $this->dataTransformer->transformAvailabilityResponse($sabreResponse);
    }

    /**
     * 带缓存的可用性查询
     */
    protected function checkAvailabilityWithCache(AvailabilityRequestDTO $request, array $params): array
    {
        // 生成缓存键
        $cacheKey = $this->generateCacheKey($request);

        // 检查是否启用缓存
        if (config('sabre.cache.enabled', true) && config('sabre.cache.availability_ttl') > 0) {
            $cached = Cache::get($cacheKey);
            if ($cached) {
                if ($this->isSabreLoggingEnabled()) {
                    $this->logSabreCacheHit('availability', $cacheKey, [
                        'hotel_id' => $params['hotelId'] ?? null,
                        'date_range' => ($params['startDate'] ?? '') . ' to ' . ($params['endDate'] ?? ''),
                    ]);
                }
                return $cached;
            } else {
                if ($this->isSabreLoggingEnabled()) {
                    $this->logSabreCacheMiss('availability', $cacheKey, [
                        'hotel_id' => $params['hotelId'] ?? null,
                        'date_range' => ($params['startDate'] ?? '') . ' to ' . ($params['endDate'] ?? ''),
                    ]);
                }
            }
        }

        try {
            $result = $this->sabreService->checkAvailability($request);

            // 缓存结果
            if (config('sabre.cache.enabled', true) && config('sabre.cache.availability_ttl') > 0) {
                Cache::put($cacheKey, $result, config('sabre.cache.availability_ttl'));
            }

            return $result;

        } catch (SabreApiException $e) {
            Log::error('Sabre Availability Service Error', [
                'message' => $e->getMessage(),
                'params' => $params,
                'context' => $e->getContext(),
            ]);

            throw $e;
        }
    }

    /**
     * 生成缓存键
     */
    protected function generateCacheKey(AvailabilityRequestDTO $request): string
    {
        $params = $request->toArray();
        ksort($params);
        return 'sabre_availability_' . md5(json_encode($params));
    }

    /**
     * 解析可用性响应数据
     */
    public function parseAvailabilityResponse(array $response): array
    {
        $parsed = [
            'success' => false,
            'hotels' => [],
            'total_count' => 0,
            'errors' => [],
        ];

        try {
            if (isset($response['productAvailability'])) {
                $parsed['success'] = true;
                $productAvailability = $response['productAvailability'];

                if (isset($productAvailability['Prices']) && is_array($productAvailability['Prices'])) {
                    foreach ($productAvailability['Prices'] as $price) {
                        $parsed['hotels'][] = $this->parseHotelPrice($price);
                    }
                    $parsed['total_count'] = count($productAvailability['Prices']);
                }
            } else {
                $parsed['errors'][] = '未找到可用性数据';
            }

        } catch (\Exception $e) {
            Log::error('Parse Availability Response Error', [
                'message' => $e->getMessage(),
                'response' => $response,
            ]);

            $parsed['errors'][] = '解析响应数据失败: ' . $e->getMessage();
        }

        return $parsed;
    }

    /**
     * 解析酒店价格数据
     */
    protected function parseHotelPrice(array $priceData): array
    {
        return [
            'product' => $priceData['Product'] ?? [],
            'price' => $priceData['Price'] ?? [],
            'availability' => $priceData['Availability'] ?? [],
            'policies' => $priceData['Policies'] ?? [],
            'amenities' => $priceData['Amenities'] ?? [],
            'images' => $priceData['Images'] ?? [],
            'description' => $priceData['Description'] ?? '',
        ];
    }

    /**
     * 清除可用性缓存
     */
    public function clearAvailabilityCache(array $params = []): bool
    {
        if (empty($params)) {
            // 清除所有可用性缓存
            return Cache::flush();
        }

        // 清除特定参数的缓存
        $request = AvailabilityRequestDTO::fromArray($params);
        $cacheKey = $this->generateCacheKey($request);

        return Cache::forget($cacheKey);
    }
}
