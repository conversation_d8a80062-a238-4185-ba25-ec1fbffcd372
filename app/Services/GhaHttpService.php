<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class GhaHttpService
{
    protected $client;
    protected $request_url;
    protected $language;
    protected $token;
    public function __construct($versions = '')
    {
        if ($versions == 'api_v2') {
            $this->request_url = config('gha.api_v2');
        }else{
            $this->request_url = config('gha.base_url');
        }
        $this->token       = config('gha.token');
        $this->language    = config('gha.base_locale');
        $this->client  = new Client([
            "headers" => [
                "Accept" => "application/json",
                "Content-Type" => "application/json",
            ],
             'verify' => false,
        ]);
    }

    /**
     * get请求
     * @param $url
     * @param $params
     * @return array|void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function get($url, $params=[])
    {
        try {
            $query_params = array_merge($params, ['token' => $this->token]);
            $query_string = http_build_query($query_params);
            $full_url = $this->request_url . $url . '?' . $query_string;
            $response = $this->client->request('GET', $full_url);
            $resp = (string)$response->getBody();
            Log::info($url.':responseData', [
                'data'  => $resp,
            ]);
            if($response->getStatusCode() == 200){
                Log::info($url.':success', ["获取数据成功"]);

                $responseData = json_decode($resp, TRUE);
                return ['data' => $responseData, 'code' => 200];
            }
        }catch (\Exception $exception){
            Log::error($url.':error', [
                'msg' => $exception->getMessage(),
            ]);
            return ['message' => $exception->getMessage(), 'code' => 500];
        }
    }

    /**
     * post请求
     * @param $url
     * @param $params
     * @return array|void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function post($url, $params, $headers=[])
    {
        try {
            $query_params = array_merge($headers, ['token' => $this->token]);
            $query_string = http_build_query($query_params);
            $response = $this->client->request('POST', $this->request_url.$url."?".$query_string, [
                'json' => $params
            ]);
            $resp = (string)$response->getBody();
            Log::info($url.':responseData', [
                'data'  => $resp,
            ]);
            if($response->getStatusCode() == 200 || $response->getStatusCode() == 201){
                Log::info($url.':success', ["获取数据成功"]);

                $responseData = json_decode($resp, true);
                return ['data' => $responseData, 'code' => 200];
            }
        }catch (\Exception $exception){
            Log::error($url.':error', [
                'msg' => $exception->getMessage(),
            ]);
            return ['message' => $exception->getMessage(), 'code' => 500];
        }
    }
}
