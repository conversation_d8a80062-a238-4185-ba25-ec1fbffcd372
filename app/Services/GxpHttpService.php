<?php

namespace App\Services;

use App\Constants\GxpAppKeyType;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class GxpHttpService
{
    protected $client;
    protected $baseUrl;
    protected $appKeyChinaKey;
    protected $appKeyRotana;

    public function __construct()
    {
        $this->baseUrl = config('gha.api_v1'); // https://gxp.stage.ghaloyalty.com/api/v1/
        $this->appKeyChinaKey = config('gha.app_key_china', '');
        $this->appKeyRotana = config('gha.app_key_rotana', '');
        $this->client = new Client([
            "headers" => [
                "Accept" => "application/json",
                "Content-Type" => "application/json",
            ],
            'verify' => false,
        ]);
    }

    /**
     * GET请求
     * @param string $url
     * @param array $params
     * @param string|null $token
     * @param string $appKeyType GxpAppKeyType::CHINA 或 GxpAppKeyType::ROTANA
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function get($url, $params = [], $token = null, $appKeyType = GxpAppKeyType::CHINA)
    {
        try {
            $headers = [
                'app-key' => $this->getAppKey($appKeyType),
            ];

            if ($token) {
                $headers['apiauthorization'] = 'Bearer ' . $token;
            }

            $query_string = http_build_query($params);
            $full_url = $this->baseUrl . $url . ($query_string ? '?' . $query_string : '');

            $response = $this->client->request('GET', $full_url, [
                'headers' => $headers
            ]);

            $resp = (string)$response->getBody();
            Log::info($url . ':responseData', [
                'data' => $resp,
            ]);

            if ($response->getStatusCode() == 200) {
                Log::info($url . ':success', ["获取数据成功"]);
                $responseData = json_decode($resp, TRUE);
                return ['data' => $responseData, 'code' => 200];
            }
        } catch (\Exception $exception) {
            Log::error($url . ':error', [
                'msg' => $exception->getMessage(),
            ]);
            return ['message' => $exception->getMessage(), 'code' => 500];
        }
    }

    /**
     * POST请求
     * @param string $url
     * @param array $params
     * @param string|null $token
     * @param string $appKeyType GxpAppKeyType::CHINA 或 GxpAppKeyType::ROTANA
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function post($url, $params, $token = null, string $appKeyType = GxpAppKeyType::CHINA)
    {
        try {
            $headers = [
                'app-key' => $this->getAppKey($appKeyType),
            ];
            if ($token) {
                $headers['apiauthorization'] = 'Bearer ' . $token;
            }

            $response = $this->client->request('POST', $this->baseUrl . $url, [
                'headers' => $headers,
                'json' => $params
            ]);

            $resp = (string)$response->getBody();
            Log::info($url . ':responseData', [
                'data' => $resp,
            ]);

            if ($response->getStatusCode() == 200 || $response->getStatusCode() == 201) {
                Log::info($url . ':success', ["获取数据成功"]);
                $responseData = json_decode($resp, true);
                return ['data' => $responseData, 'code' => 200];
            }
        } catch (\Exception $exception) {
            Log::error($url . ':error', [
                'msg' => $exception->getMessage(),
            ]);
            return ['message' => $exception->getMessage(), 'code' => 500];
        }
    }

    /**
     * 根据类型获取对应的app-key
     *
     * @param string $type
     * @return string
     * @throws \Exception
     */
    protected function getAppKey(string $type): string
    {
        if (!GxpAppKeyType::isValidType($type)) {
            throw new \Exception("不支持的app-key类型: {$type}");
        }

        switch ($type) {
            case GxpAppKeyType::CHINA:
                return $this->appKeyChinaKey;
            case GxpAppKeyType::ROTANA:
                return $this->appKeyRotana;
            default:
                throw new \Exception("不支持的app-key类型: {$type}");
        }
    }
}
