<?php

namespace App\Services;

use App\Constants\GxpAppKeyType;
use App\DTOs\Gxp\CurrencyExchangeRateDTO;
use App\DTOs\Gxp\GuestTokenDTO;
use App\DTOs\Gxp\ReservationDTO;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class GxpService
{
    protected GxpHttpService $httpService;
    protected ?string $username = null;
    protected ?string $password = null;
    protected string $cacheKeyPrefix = 'gxp_token_';
    protected bool $initialized = false;

    public function __construct(GxpHttpService $httpService)
    {
        $this->httpService = $httpService;
        $this->initializeFromConfig();
    }

    /**
     * 从配置文件初始化凭据
     *
     * @throws Exception
     */
    protected function initializeFromConfig(): void
    {
        $this->username = config('gha.gxp.username');
        $this->password = config('gha.gxp.password');

        if ($this->username && $this->password) {
            $this->initialized = true;
            Log::info('GXP服务已从配置文件初始化', ['username' => $this->username]);
        } else {
            Log::warning('GXP配置文件中缺少用户凭据', [
                'has_username' => !empty($this->username),
                'has_password' => !empty($this->password)
            ]);
        }
    }

    /**
     * 检查服务是否已初始化
     *
     * @return bool
     */
    public function isInitialized(): bool
    {
        return $this->initialized && $this->username && $this->password;
    }

    /**
     * 手动设置GXP用户凭据（覆盖配置文件）
     *
     * @param string $username
     * @param string $password
     * @return self
     */
    public function setCredentials(string $username, string $password): self
    {
        $this->username = $username;
        $this->password = $password;
        $this->initialized = true;

        Log::info('GXP凭据已手动设置', ['username' => $username]);
        return $this;
    }

    /**
     * 检查是否已设置凭据
     *
     * @throws Exception
     */
    protected function ensureCredentialsSet(): void
    {
        if (!$this->isInitialized()) {
            throw new Exception('GXP服务未初始化，请检查配置文件中的gha.gxp.username和gha.gxp.password配置');
        }
    }

    /**
     * 自动获取或刷新token（内部使用）
     *
     * @return string
     * @throws Exception
     */
    protected function getAuthToken(): string
    {
        $this->ensureCredentialsSet();
        return $this->getOrRefreshToken($this->username, $this->password);
    }

    /**
     * 获取或刷新访客令牌（带缓存）
     *
     * @param string $username
     * @param string $password
     * @return string
     * @throws Exception
     */
    protected function getOrRefreshToken(string $username, string $password): string
    {
        $cacheKey = $this->cacheKeyPrefix . md5($username . $password);

        // 尝试从缓存获取token
        $cachedData = Cache::get($cacheKey);

        if ($cachedData && isset($cachedData['token']) && isset($cachedData['expires_at'])) {
            // 检查token是否还有效（提前5分钟过期）
            if (time() < ($cachedData['expires_at'] - 300)) {
                Log::info('使用缓存的GXP token', ['username' => $username]);
                return $cachedData['token'];
            }
        }

        // 缓存中没有有效token，重新获取
        Log::info('重新获取GXP token', ['username' => $username]);
        $tokenDto = $this->getGuestToken($username, $password);

        if (!$tokenDto->isSuccess() || !$tokenDto->token) {
            throw new Exception('获取访客令牌失败: ' . ($tokenDto->error ?? '未知错误'));
        }

        // 缓存token（默认缓存55分钟，token通常1小时过期）
        $expiresAt = time() + ($tokenDto->expiresIn ?? 3600);
        Cache::put($cacheKey, [
            'token' => $tokenDto->token,
            'expires_at' => $expiresAt,
            'token_type' => $tokenDto->tokenType
        ], ($tokenDto->expiresIn ?? 3600) - 300); // 提前5分钟过期

        return $tokenDto->token;
    }

    /**
     * 获取访客令牌
     *
     * @param string $username
     * @param string $password
     * @param string $type
     * @return GuestTokenDTO
     * @throws Exception
     */
    public function getGuestToken(string $username, string $password, string $type = 'PARTNER'): GuestTokenDTO
    {
        try {
            $params = [
                'type' => $type,
                'username' => $username,
                'password' => $password
            ];

            // Guest Token接口使用china app-key
            $response = $this->httpService->post('auth/token', $params, null, GxpAppKeyType::CHINA);

            if ($response['code'] !== 200) {
                throw new Exception($response['message'] ?? '获取访客令牌失败');
            }
            return GuestTokenDTO::fromArray(Arr::get($response, 'data.data'));

        } catch (Exception $e) {
            Log::error('GXP获取访客令牌失败', [
                'username' => $username,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取汇率转换
     *
     * @param string $currency
     * @param string $startDate
     * @param int $size
     * @return array
     * @throws Exception
     */
    public function getCurrencyExchangeRate(string $currency, string $startDate, int $size = 10): array
    {
        try {
            // 自动获取token
            $token = $this->getAuthToken();

            $params = [
                'currency' => $currency,
                'start_date' => $startDate,
                'size' => $size
            ];

            // Currency Exchange Rate接口使用rotana app-key
            $response = $this->httpService->get('ddollars/exchange-rate', $params, $token, GxpAppKeyType::ROTANA);

            // 检查响应状态
            if (!isset($response['status']) || $response['status'] !== 200) {
                throw new Exception($response['message'] ?? '获取汇率失败');
            }

            // 处理响应数据，data直接是汇率数组
            $rates = [];
            if (isset($response['data']) && is_array($response['data'])) {
                foreach ($response['data'] as $rateData) {
                    $rates[] = CurrencyExchangeRateDTO::fromArray($rateData);
                }
            }

            return [
                'status' => $response['status'],
                'code' => $response['code'] ?? 'SUCCESS',
                'message' => $response['message'] ?? 'Successfully retrieved resource',
                'timestamp' => $response['timestamp'] ?? null,
                'path' => $response['path'] ?? null,
                'rates' => $rates,
                'total_records' => count($rates)
            ];

        } catch (Exception $e) {
            Log::error('GXP获取汇率失败', [
                'username' => $this->username,
                'currency' => $currency,
                'start_date' => $startDate,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 提交预订到仓库
     *
     * @param array $reservationData
     * @return array
     * @throws Exception
     */
    public function submitReservation(array $reservationData): array
    {
        try {
            // 自动获取token
            $token = $this->getAuthToken();

            // 设置默认值
            $params = array_merge([
                'source' => 'GHA'
            ], $reservationData);

            // Reserve repo posting接口使用china app-key
            $response = $this->httpService->post('reservations-repo', $params, $token, GxpAppKeyType::CHINA);

            if ($response['code'] !== 200) {
                throw new Exception($response['message'] ?? '预订提交失败');
            }

            // 转换为DTO
            $reservationDto = ReservationDTO::fromArray($response['data']);

            return [
                'reservation' => $reservationDto,
                'success' => true
            ];

        } catch (Exception $e) {
            Log::error('GXP预订提交失败', [
                'username' => $this->username,
                'itinerary_number' => $reservationData['itinerary_number'] ?? null,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取用户预订列表
     *
     * @param array $filters
     * @return array
     * @throws Exception
     */
    public function getUserBookings(array $filters = []): array
    {
        try {
            // 自动获取token
            $token = $this->getAuthToken();

            // 设置默认参数
            $params = array_merge([
                'page' => 0,
                'size' => 20,
                'sort_by' => 'ArrivalDate',
                'sort_direction' => 'desc',
                'status' => ['CURRENT', 'UPCOMING', 'PAST', 'CANCELED']
            ], $filters);

            // My Booking接口使用china app-key
            $response = $this->httpService->get('reservations-repo', $params, $token, GxpAppKeyType::CHINA);

            if ($response['code'] !== 200) {
                throw new Exception($response['message'] ?? '获取预订列表失败');
            }

            // 转换为DTO数组
            $bookings = [];
            if (isset($response['data']['bookings']) && is_array($response['data']['bookings'])) {
                foreach ($response['data']['bookings'] as $bookingData) {
                    $bookings[] = ReservationDTO::fromArray($bookingData);
                }
            }

            return [
                'bookings' => $bookings,
                'pagination' => $response['data']['pagination'] ?? null,
                'total' => $response['data']['total'] ?? count($bookings)
            ];

        } catch (Exception $e) {
            Log::error('GXP获取预订列表失败', [
                'username' => $this->username,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 清除指定用户的token缓存
     *
     * @param string $username
     * @param string $password
     * @return bool
     */
    public function clearTokenCache(string $username, string $password): bool
    {
        $cacheKey = $this->cacheKeyPrefix . md5($username . $password);
        return Cache::forget($cacheKey);
    }

    /**
     * 清除当前用户的token缓存
     *
     * @return bool
     * @throws Exception
     */
    public function clearCurrentTokenCache(): bool
    {
        $this->ensureCredentialsSet();
        return $this->clearTokenCache($this->username, $this->password);
    }

    /**
     * 清除所有GXP token缓存
     *
     * @return bool
     */
    public function clearAllTokenCache(): bool
    {
        try {
            // 获取所有以gxp_token_开头的缓存键
            $pattern = $this->cacheKeyPrefix . '*';

            // 注意：这个实现依赖于缓存驱动，Redis支持pattern删除
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                $keys = $redis->keys($pattern);
                if (!empty($keys)) {
                    $redis->del($keys);
                }
                return true;
            }

            // 对于其他缓存驱动，暂时返回true
            Log::warning('无法批量清除GXP token缓存，当前缓存驱动不支持pattern删除');
            return true;

        } catch (Exception $e) {
            Log::error('清除GXP token缓存失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 获取token缓存信息
     *
     * @param string $username
     * @param string $password
     * @return array|null
     */
    public function getTokenCacheInfo(string $username, string $password): ?array
    {
        $cacheKey = $this->cacheKeyPrefix . md5($username . $password);
        $cachedData = Cache::get($cacheKey);

        if (!$cachedData) {
            return null;
        }

        return [
            'has_token' => isset($cachedData['token']),
            'expires_at' => $cachedData['expires_at'] ?? null,
            'expires_in_seconds' => isset($cachedData['expires_at']) ?
                max(0, $cachedData['expires_at'] - time()) : null,
            'token_type' => $cachedData['token_type'] ?? null
        ];
    }

    /**
     * 获取当前用户的token缓存信息
     *
     * @return array|null
     * @throws Exception
     */
    public function getCurrentTokenCacheInfo(): ?array
    {
        $this->ensureCredentialsSet();
        return $this->getTokenCacheInfo($this->username, $this->password);
    }

    /**
     * 验证预订数据
     *
     * @param array $data
     * @return bool
     * @throws Exception
     */
    public function validateReservationData(array $data): bool
    {
        $required = ['itinerary_number', 'status', 'chain_id', 'hotel_id', 'reservation_details'];

        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("缺少必需字段: {$field}");
            }
        }

        // 验证状态值
        $validStatuses = ['CREATE', 'MODIFY', 'CANCEL'];
        if (!in_array($data['status'], $validStatuses)) {
            throw new Exception('无效的状态值: ' . $data['status']);
        }

        // 验证预订详情
        if (!is_array($data['reservation_details']) || empty($data['reservation_details'])) {
            throw new Exception('预订详情必须是非空数组');
        }

        foreach ($data['reservation_details'] as $detail) {
            if (!isset($detail['confirmation_number']) || !isset($detail['member_rate_yn'])) {
                throw new Exception('预订详情缺少必需字段');
            }
        }

        return true;
    }

    /**
     * 验证汇率查询参数
     *
     * @param string $currency
     * @param string $startDate
     * @param int $size
     * @return bool
     * @throws Exception
     */
    public function validateCurrencyParams(string $currency, string $startDate, int $size): bool
    {
        if (empty($currency)) {
            throw new Exception('货币代码不能为空');
        }

        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate)) {
            throw new Exception('日期格式必须为YYYY-MM-DD');
        }

        if ($size < 1 || $size > 100) {
            throw new Exception('size参数必须在1-100之间');
        }

        return true;
    }
}
