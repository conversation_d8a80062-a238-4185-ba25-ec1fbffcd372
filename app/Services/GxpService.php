<?php

namespace App\Services;

use App\Constants\GxpAppKeyType;
use App\DTOs\Gxp\CurrencyExchangeRateDTO;
use App\DTOs\Gxp\GuestTokenDTO;
use App\DTOs\Gxp\ReservationDTO;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class GxpService
{
    protected GxpHttpService $httpService;
    protected ?string $cachedToken = null;
    protected ?int $tokenExpiresAt = null;
    protected string $cacheKeyPrefix = 'gxp_token_';

    // 新增：初始化时的认证信息
    protected ?string $initializedUsername = null;
    protected ?string $initializedPassword = null;
    protected bool $isInitialized = false;

    /**
     *
     *初始化
     * @param GxpHttpService $httpService
     * @param bool|null $autoInitialize 是否自动初始化token，如果不提供则从配置文件读取
     * @throws Exception
     */
    public function __construct(GxpHttpService $httpService, ?bool $autoInitialize = null)
    {
        $this->httpService = $httpService;

        // 从配置文件获取值
        $configUsername = config('gha.gxp.username');
        $configPassword = config('gha.gxp.password');
        $configAutoInitialize = config('gha.gxp.auto_initialize', true);

        // 确定是否自动初始化
        $useAutoInitialize = $autoInitialize !== null ? $autoInitialize : $configAutoInitialize;

        // 如果配置了用户名和密码，则进行初始化
        if ($configUsername && $configPassword && $useAutoInitialize) {
            $this->initialize();
        }
    }

        /**
     * 初始化服务并获取token
     *
     * @return bool
     * @throws Exception
     */
    public function initialize(): bool
    {
        try {
            // 从配置文件获取认证信息
            $username = config('gha.gxp.username');
            $password = config('gha.gxp.password');

            if (!$username || !$password) {
                throw new Exception('配置文件中缺少GXP认证信息');
            }

            $this->initializedUsername = $username;
            $this->initializedPassword = $password;

            // 获取token并缓存
            $token = $this->getOrRefreshToken($username, $password);

            if ($token) {
                $this->isInitialized = true;
                Log::info('GXP服务初始化成功', ['username' => $username]);
                return true;
            }

            return false;
        } catch (Exception $e) {
            Log::error('GXP服务初始化失败', [
                'username' => $this->initializedUsername ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 检查服务是否已初始化
     *
     * @return bool
     */
    public function isInitialized(): bool
    {
        return $this->isInitialized && $this->initializedUsername && $this->initializedPassword;
    }

    /**
     * 获取当前初始化的用户名
     *
     * @return string|null
     */
    public function getInitializedUsername(): ?string
    {
        return $this->initializedUsername;
    }

        /**
     * 重新初始化token（当token过期时）
     *
     * @return bool
     * @throws Exception
     */
    public function reinitialize(): bool
    {
        // 直接调用initialize方法，它会从配置文件中读取认证信息
        return $this->initialize();
    }

    /**
     * 获取或刷新访客令牌（带缓存）
     *
     * @param string $username
     * @param string $password
     * @return string
     * @throws Exception
     */
    protected function getOrRefreshToken(string $username, string $password): string
    {
        $cacheKey = $this->cacheKeyPrefix . md5($username . $password);

        // 尝试从缓存获取token
        $cachedData = Cache::get($cacheKey);

        if ($cachedData && isset($cachedData['token']) && isset($cachedData['expires_in'])) {
            // 检查token是否还有效（提前5分钟过期）
            if (time() < ($cachedData['expires_in'] - 300)) {
                Log::info('使用缓存的GXP token', ['username' => $username]);
                return $cachedData['token'];
            }
        }

        // 缓存中没有有效token，重新获取
        Log::info('重新获取GXP token', ['username' => $username]);
        $tokenDto = $this->getGuestToken($username, $password);

        if (!$tokenDto->isSuccess() || !$tokenDto->token) {
            throw new Exception('获取访客令牌失败: ' . ($tokenDto->error ?? '未知错误'));
        }

        // 缓存token（默认缓存55分钟，token通常1小时过期）
        $expiresAt = time() + ($tokenDto->expiresIn ?? 3600);
        Cache::put($cacheKey, [
            'token' => $tokenDto->token,
            'expires_at' => $expiresAt,
            'token_type' => $tokenDto->tokenType
        ], ($tokenDto->expiresIn ?? 3600) - 300); // 提前5分钟过期

        return $tokenDto->token;
    }

        /**
     * 获取当前token（如果已初始化）
     *
     * @return string|null
     * @throws Exception
     */
    protected function getCurrentToken(): ?string
    {
        if (!$this->isInitialized()) {
            throw new Exception('服务未初始化，请先调用initialize()方法');
        }

        return $this->getOrRefreshToken($this->initializedUsername, $this->initializedPassword);
    }

        /**
     * 创建已初始化的GXP服务实例
     *
     * @return static
     * @throws Exception
     */
    public static function createInitialized(): static
    {
        $httpService = app(GxpHttpService::class);
        $instance = new static($httpService, true);

        if (!$instance->isInitialized()) {
            throw new Exception('GXP服务初始化失败，请检查配置文件中的认证信息');
        }

        return $instance;
    }

    /**
     * 获取访客令牌
     *
     * @param string $username
     * @param string $password
     * @param string $type
     * @return GuestTokenDTO
     * @throws Exception
     */
    public function getGuestToken(string $username, string $password, string $type = 'PARTNER'): GuestTokenDTO
    {
        try {
            $params = [
                'type' => $type,
                'username' => $username,
                'password' => $password
            ];

            // Guest Token接口使用china app-key
            $response = $this->httpService->post('auth/token', $params, null, GxpAppKeyType::CHINA);

            if ($response['code'] !== 200) {
                throw new Exception($response['message'] ?? '获取访客令牌失败');
            }

            return GuestTokenDTO::fromArray($response['data']);

        } catch (Exception $e) {
            Log::error('GXP获取访客令牌失败', [
                'username' => $username,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

        /**
     * 获取汇率转换
     *
     * @param string $currency
     * @param string $startDate
     * @param int $size
     * @return array
     * @throws Exception
     */
    public function getCurrencyExchangeRate(string $currency, string $startDate, int $size = 10): array
    {
        try {
            if (!$this->isInitialized()) {
                throw new Exception('服务未初始化，请先调用initialize()方法');
            }

            // 获取或刷新缓存的token
            $token = $this->getCurrentToken();

            $params = [
                'currency' => $currency,
                'start_date' => $startDate,
                'size' => $size
            ];

            // Currency Exchange Rate接口使用rotana app-key
            $response = $this->httpService->get('ddollars/exchange-rate', $params, $token, GxpAppKeyType::ROTANA);

            if ($response['code'] !== 200) {
                throw new Exception($response['message'] ?? '获取汇率失败');
            }

            // 如果响应数据是数组，转换为DTO数组
            $rates = [];
            if (isset($response['data']['rates']) && is_array($response['data']['rates'])) {
                foreach ($response['data']['rates'] as $rateData) {
                    $rates[] = CurrencyExchangeRateDTO::fromArray($rateData);
                }
            }

            return [
                'rates' => $rates,
                'metadata' => $response['data']['metadata'] ?? null
            ];

        } catch (Exception $e) {
            Log::error('GXP获取汇率失败', [
                'username' => $this->initializedUsername ?? 'unknown',
                'currency' => $currency,
                'start_date' => $startDate,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

        /**
     * 提交预订到仓库
     *
     * @param array $reservationData
     * @return array
     * @throws Exception
     */
    public function submitReservation(array $reservationData): array
    {
        try {
            if (!$this->isInitialized()) {
                throw new Exception('服务未初始化，请先调用initialize()方法');
            }

            // 获取或刷新缓存的token
            $token = $this->getCurrentToken();

            // 设置默认值
            $params = array_merge([
                'source' => 'GHA'
            ], $reservationData);

            // Reserve repo posting接口使用china app-key
            $response = $this->httpService->post('reservations-repo', $params, $token, GxpAppKeyType::CHINA);

            if ($response['code'] !== 200) {
                throw new Exception($response['message'] ?? '预订提交失败');
            }

            return $response['data'];

        } catch (Exception $e) {
            Log::error('GXP预订提交失败', [
                'username' => $this->initializedUsername ?? 'unknown',
                'itinerary_number' => $reservationData['itinerary_number'] ?? null,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

        /**
     * 获取用户预订列表
     *
     * @param array $filters
     * @return array
     * @throws Exception
     */
    public function getUserBookings(array $filters = []): array
    {
        try {
            if (!$this->isInitialized()) {
                throw new Exception('服务未初始化，请先调用initialize()方法');
            }

            // 获取或刷新缓存的token
            $token = $this->getCurrentToken();

            // 设置默认参数
            $params = array_merge([
                'page' => 0,
                'size' => 20,
                'sort_by' => 'ArrivalDate',
                'sort_direction' => 'desc',
                'status' => ['CURRENT', 'UPCOMING', 'PAST', 'CANCELED']
            ], $filters);

            // My Booking接口使用china app-key
            $response = $this->httpService->get('reservations-repo', $params, $token, GxpAppKeyType::CHINA);

            if ($response['code'] !== 200) {
                throw new Exception($response['message'] ?? '获取预订列表失败');
            }

            // 转换预订数据为DTO
            $reservations = [];
            if (isset($response['data']['reservations']) && is_array($response['data']['reservations'])) {
                foreach ($response['data']['reservations'] as $reservationData) {
                    $reservations[] = ReservationDTO::fromArray($reservationData);
                }
            }

            return [
                'reservations' => $reservations,
                'pagination' => $response['data']['pagination'] ?? null,
                'total' => $response['data']['total'] ?? count($reservations)
            ];

        } catch (Exception $e) {
            Log::error('GXP获取预订列表失败', [
                'username' => $this->initializedUsername ?? 'unknown',
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }



    /**
     * 验证预订数据
     *
     * @param array $data
     * @return bool
     * @throws Exception
     */
    public function validateReservationData(array $data): bool
    {
        $required = ['itinerary_number', 'status', 'chain_id', 'hotel_id', 'reservation_details'];

        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("缺少必需字段: {$field}");
            }
        }

        // 验证状态值
        $validStatuses = ['CREATE', 'MODIFY', 'CANCEL'];
        if (!in_array($data['status'], $validStatuses)) {
            throw new Exception('无效的预订状态');
        }

        // 验证预订详情
        if (!is_array($data['reservation_details']) || empty($data['reservation_details'])) {
            throw new Exception('预订详情不能为空');
        }

        foreach ($data['reservation_details'] as $detail) {
            if (!isset($detail['confirmation_number']) || !isset($detail['member_rate_yn'])) {
                throw new Exception('预订详情格式不正确');
            }
        }

        return true;
    }

    /**
     * 验证汇率查询参数
     *
     * @param string $currency
     * @param string $startDate
     * @param int $size
     * @return bool
     * @throws Exception
     */
    public function validateCurrencyParams(string $currency, string $startDate, int $size): bool
    {
        if (empty($currency)) {
            throw new Exception('货币代码不能为空');
        }

        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate)) {
            throw new Exception('日期格式不正确，应为YYYY-MM-DD');
        }

        if ($size < 1 || $size > 100) {
            throw new Exception('查询数量应在1-100之间');
        }

        return true;
    }

    /**
     * 清除指定用户的token缓存
     *
     * @param string $username
     * @param string $password
     * @return bool
     */
    public function clearTokenCache(string $username, string $password): bool
    {
        $cacheKey = $this->cacheKeyPrefix . md5($username . $password);
        return Cache::forget($cacheKey);
    }

    /**
     * 清除所有GXP token缓存
     *
     * @return bool
     */
    public function clearAllTokenCache(): bool
    {
        try {
            // 获取所有以gxp_token_开头的缓存键
            $pattern = $this->cacheKeyPrefix . '*';

            // 注意：这个实现依赖于缓存驱动，Redis支持pattern删除
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                $keys = $redis->keys($pattern);
                if (!empty($keys)) {
                    $redis->del($keys);
                }
                return true;
            }

            // 对于其他缓存驱动，暂时返回true
            Log::warning('无法批量清除GXP token缓存，当前缓存驱动不支持pattern删除');
            return true;

        } catch (Exception $e) {
            Log::error('清除GXP token缓存失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 获取token缓存信息
     *
     * @param string $username
     * @param string $password
     * @return array|null
     */
    public function getTokenCacheInfo(string $username, string $password): ?array
    {
        $cacheKey = $this->cacheKeyPrefix . md5($username . $password);
        $cachedData = Cache::get($cacheKey);

        if (!$cachedData) {
            return null;
        }

        return [
            'has_token' => isset($cachedData['token']),
            'expires_at' => $cachedData['expires_at'] ?? null,
            'expires_in_seconds' => isset($cachedData['expires_at']) ?
                max(0, $cachedData['expires_at'] - time()) : null,
            'token_type' => $cachedData['token_type'] ?? null
        ];
    }
}
