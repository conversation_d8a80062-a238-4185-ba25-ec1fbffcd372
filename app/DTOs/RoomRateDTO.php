<?php

namespace App\DTOs;

class RoomRateDTO extends BaseDTO
{
    public string $roomCode;
    public string $rateCode;
    public string $rateName;
    public string $rateDescription;
    public string $guaranteePolicy;
    public string $currency;
    public float $price;
    public float $fee;
    public float $tax;
    public float $total_price;
    public int $cny_price;
    public int $cny_fee;
    public int $cny_tax;
    public int $cny_total_price;
    public string $cancelRuleString;
    public bool $isMemberRate;
    public array $dailyPrices;

    public function __construct(
        string $roomCode,
        string $rateCode,
        string $rateName,
        string $rateDescription,
        string $guaranteePolicy,
        string $currency,
        float $price,
        float $fee,
        float $tax,
        float $total_price,
        int $cny_price,
        int $cny_fee,
        int $cny_tax,
        int $cny_total_price,
        string $cancelRuleString,
        bool $isMemberRate = false,
        array $dailyPrices = []
    ) {
        $this->roomCode = $roomCode;
        $this->rateCode = $rateCode;
        $this->rateName = $rateName;
        $this->rateDescription = $rateDescription;
        $this->guaranteePolicy = $guaranteePolicy;
        $this->currency = $currency;
        $this->price = $price;
        $this->fee = $fee;
        $this->tax = $tax;
        $this->total_price = $total_price;
        $this->cny_price = $cny_price;
        $this->cny_fee = $cny_fee;
        $this->cny_tax = $cny_tax;
        $this->cny_total_price = $cny_total_price;
        $this->cancelRuleString = $cancelRuleString;
        $this->isMemberRate = $isMemberRate;
        $this->dailyPrices = $dailyPrices;
    }

    public function toArray(): array
    {
        return [
            'roomCode' => $this->roomCode,
            'rateCode' => $this->rateCode,
            'rateName' => $this->rateName,
            'rateDescription' => $this->rateDescription,
            'guaranteePolicy' => $this->guaranteePolicy,
            'currency' => $this->currency,
            'price' => $this->price,
            'fee' => $this->fee,
            'tax' => $this->tax,
            'total_price' => $this->total_price,
            'cny_price' => $this->cny_price,
            'cny_fee' => $this->cny_fee,
            'cny_tax' => $this->cny_tax,
            'cny_total_price' => $this->cny_total_price,
            'cancelRuleString' => $this->cancelRuleString,
            'isMemberRate' => $this->isMemberRate,
            'dailyPrices' => array_map(fn($price) => $price->toArray(), $this->dailyPrices),
        ];
    }

    public static function fromArray(array $data): self
    {
        self::validateRequiredFields($data, [
            'roomCode', 'rateCode', 'rateName', 'rateDescription',
            'guaranteePolicy', 'currency', 'cancelRuleString'
        ]);
        return new self(
            self::toString(self::getField($data, 'roomCode')),
            self::toString(self::getField($data, 'rateCode')),
            self::toString(self::getField($data, 'rateName')),
            self::toString(self::getField($data, 'rateDescription')),
            self::toString(self::getField($data, 'guaranteePolicy')),
            self::toString(self::getField($data, 'currency')),
            self::toFloat(self::getField($data, 'price')),
            self::toFloat(self::getField($data, 'fee')),
            self::toFloat(self::getField($data, 'tax')),
            self::toFloat(self::getField($data, 'total_price')),
            self::toInt(self::getField($data, 'cny_price')),
            self::toInt(self::getField($data, 'cny_fee')),
            self::toInt(self::getField($data, 'cny_tax')),
            self::toInt(self::getField($data, 'cny_total_price')),
            self::toString(self::getField($data, 'cancelRuleString')),
            self::toBool(self::getField($data, 'isMemberRate', false)),
            array_map(
                fn($priceData) => DailyPriceDTO::fromArray($priceData),
                self::toArrayType(self::getField($data, 'dailyPrices', []))
            )
        );
    }
}
