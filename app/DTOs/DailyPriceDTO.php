<?php

namespace App\DTOs;

class DailyPriceDTO extends BaseDTO
{
    public string $date;
    public string $currency;
    public float $price;
    public float $fee;
    public float $tax;
    public float $total_price;
    public int $cny_price;
    public int $cny_fee;
    public int $cny_tax;
    public int $cny_total_price;
    public int $availableInventory;

    public function __construct(
        string $date,
        string $currency,
        float $price,
        float $fee,
        float $tax,
        float $total_price,
        int $cny_price,
        int $cny_fee,
        int $cny_tax,
        int $cny_total_price,
        int $availableInventory
    ) {
        $this->date = $date;
        $this->currency = $currency;
        $this->price = $price;
        $this->fee = $fee;
        $this->tax = $tax;
        $this->total_price = $total_price;
        $this->cny_price = $cny_price;
        $this->cny_fee = $cny_fee;
        $this->cny_tax = $cny_tax;
        $this->cny_total_price = $cny_total_price;
        $this->availableInventory = $availableInventory;
    }

    public function toArray(): array
    {
        return [
            'date' => $this->date,
            'currency' => $this->currency,
            'price' => $this->price,
            'fee' => $this->fee,
            'tax' => $this->tax,
            'total_price' => $this->total_price,
            'cny_price' => $this->cny_price,
            'cny_fee' => $this->cny_fee,
            'cny_tax' => $this->cny_tax,
            'cny_total_price' => $this->cny_total_price,
            'availableInventory' => $this->availableInventory,
        ];
    }

    public static function fromArray(array $data): self
    {
        self::validateRequiredFields($data, [
            'date', 'currency'
        ]);

        return new self(
            self::toString(self::getField($data, 'date')),
            self::toString(self::getField($data, 'currency')),
            self::toFloat(self::getField($data, 'price')),
            self::toFloat(self::getField($data, 'fee')),
            self::toFloat(self::getField($data, 'tax')),
            self::toFloat(self::getField($data, 'total_price')),
            self::toInt(self::getField($data, 'cny_price')),
            self::toInt(self::getField($data, 'cny_fee')),
            self::toInt(self::getField($data, 'cny_tax')),
            self::toInt(self::getField($data, 'cny_total_price')),
            self::toInt(self::getField($data, 'availableInventory'))
        );
    }
}
