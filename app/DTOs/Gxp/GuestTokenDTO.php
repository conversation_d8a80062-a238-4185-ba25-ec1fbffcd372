<?php

namespace App\DTOs\Gxp;

class GuestTokenDTO
{
    public ?string $token;
    public ?string $tokenType;
    public ?int $expiresIn;
    public ?string $refreshToken;
    public ?string $scope;
    public ?array $userInfo;
    public ?string $error;
    public ?string $errorDescription;

    public function __construct(
        ?string $token = null,
        ?string $tokenType = null,
        ?int $expiresIn = null,
        ?string $refreshToken = null,
        ?string $scope = null,
        ?array $userInfo = null,
        ?string $error = null,
        ?string $errorDescription = null
    ) {
        $this->token = $token;
        $this->tokenType = $tokenType;
        $this->expiresIn = $expiresIn;
        $this->refreshToken = $refreshToken;
        $this->scope = $scope;
        $this->userInfo = $userInfo;
        $this->error = $error;
        $this->errorDescription = $errorDescription;
    }

    public function toArray(): array
    {
        return [
            'token' => $this->token,
            'tokenType' => $this->tokenType,
            'expiresIn' => $this->expiresIn,
            'refreshToken' => $this->refreshToken,
            'scope' => $this->scope,
            'userInfo' => $this->userInfo,
            'error' => $this->error,
            'errorDescription' => $this->errorDescription,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['token'] ?? $data['access_token'] ?? null,
            $data['tokenType'] ?? $data['token_type'] ?? null,
            isset($data['expiresIn']) ? (int)$data['expiresIn'] : (isset($data['expires_in']) ? (int)$data['expires_in'] : null),
            $data['refreshToken'] ?? $data['refresh_token'] ?? null,
            $data['scope'] ?? null,
            $data['userInfo'] ?? $data['user_info'] ?? null,
            $data['error'] ?? null,
            $data['errorDescription'] ?? $data['error_description'] ?? null
        );
    }

    public function isSuccess(): bool
    {
        return $this->token !== null && $this->error === null;
    }
}
