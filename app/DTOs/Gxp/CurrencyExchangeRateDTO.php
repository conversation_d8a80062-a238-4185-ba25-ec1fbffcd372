<?php

namespace App\DTOs\Gxp;

class CurrencyExchangeRateDTO
{
    public string $currencyCode;
    public float $conversionRateToUsd;
    public string $startDate;
    public ?string $endDate;

    public function __construct(
        string $currencyCode,
        float $conversionRateToUsd,
        string $startDate,
        ?string $endDate = null
    ) {
        $this->currencyCode = $currencyCode;
        $this->conversionRateToUsd = $conversionRateToUsd;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function toArray(): array
    {
        return [
            'currency_code' => $this->currencyCode,
            'conversion_rate_to_usd' => $this->conversionRateToUsd,
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['currency_code'] ?? '',
            (float)($data['conversion_rate_to_usd'] ?? 0),
            $data['start_date'] ?? '',
            $data['end_date'] ?? null
        );
    }

    /**
     * 获取格式化的汇率信息
     *
     * @return string
     */
    public function getFormattedRate(): string
    {
        return number_format($this->conversionRateToUsd, 6);
    }

    /**
     * 检查汇率是否在指定日期范围内有效
     *
     * @param string $date
     * @return bool
     */
    public function isValidForDate(string $date): bool
    {
        $checkDate = strtotime($date);
        $startTime = strtotime($this->startDate);

        if ($this->endDate) {
            $endTime = strtotime($this->endDate);
            return $checkDate >= $startTime && $checkDate < $endTime;
        }

        // 如果没有结束日期，表示当前有效
        return $checkDate >= $startTime;
    }

    /**
     * 计算指定金额的USD等值
     *
     * @param float $amount
     * @return float
     */
    public function convertToUsd(float $amount): float
    {
        return $amount * $this->conversionRateToUsd;
    }

    /**
     * 计算USD转换为当前货币的金额
     *
     * @param float $usdAmount
     * @return float
     */
    public function convertFromUsd(float $usdAmount): float
    {
        return $this->conversionRateToUsd > 0 ? $usdAmount / $this->conversionRateToUsd : 0;
    }
}
