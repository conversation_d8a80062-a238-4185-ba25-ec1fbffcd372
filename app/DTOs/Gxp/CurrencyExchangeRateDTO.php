<?php

namespace App\DTOs\Gxp;

class CurrencyExchangeRateDTO
{
    public string $currency;
    public float $exchangeRate;
    public string $effectiveDate;
    public ?string $baseCurrency;
    public ?array $metadata;

    public function __construct(
        string $currency,
        float $exchangeRate,
        string $effectiveDate,
        ?string $baseCurrency = null,
        ?array $metadata = null
    ) {
        $this->currency = $currency;
        $this->exchangeRate = $exchangeRate;
        $this->effectiveDate = $effectiveDate;
        $this->baseCurrency = $baseCurrency;
        $this->metadata = $metadata;
    }

    public function toArray(): array
    {
        return [
            'currency' => $this->currency,
            'exchangeRate' => $this->exchangeRate,
            'effectiveDate' => $this->effectiveDate,
            'baseCurrency' => $this->baseCurrency,
            'metadata' => $this->metadata,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['currency'] ?? '',
            (float)($data['exchangeRate'] ?? $data['exchange_rate'] ?? 0),
            $data['effectiveDate'] ?? $data['effective_date'] ?? '',
            $data['baseCurrency'] ?? $data['base_currency'] ?? null,
            $data['metadata'] ?? null
        );
    }
}
