<?php

namespace App\DTOs;

class RoomDTO extends BaseDTO
{
    public string $roomCode;
    public string $roomName;
    public string $roomDescription;
    public array $roomRateList;

    public function __construct(
        string $roomCode,
        string $roomName,
        string $roomDescription,
        array $roomRateList
    ) {
        $this->roomCode = $roomCode;
        $this->roomName = $roomName;
        $this->roomDescription = $roomDescription;
        $this->roomRateList = $roomRateList;
    }

    public function toArray(): array
    {
        return [
            'roomCode' => $this->roomCode,
            'roomName' => $this->roomName,
            'roomDescription' => $this->roomDescription,
            'roomRateList' => array_map(fn($rate) => $rate->toArray(), $this->roomRateList)
        ];
    }

    public static function fromArray(array $data): self
    {
        self::validateRequiredFields($data, ['roomCode', 'roomName', 'roomDescription']);

        $roomRateList = array_map(
            fn($rateData) => RoomRateDTO::fromArray($rateData),
            self::toArrayType(self::getField($data, 'roomRateList', []))
        );

        return new self(
            self::toString(self::getField($data, 'roomCode')),
            self::toString(self::getField($data, 'roomName')),
            self::toString(self::getField($data, 'roomDescription')),
            $roomRateList
        );
    }
}
