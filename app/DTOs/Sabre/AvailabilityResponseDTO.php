<?php

namespace App\DTOs\Sabre;

class AvailabilityResponseDTO extends BaseSabreDTO
{
    public function __construct(
        public readonly bool $success,
        public readonly ?ProductAvailabilityDTO $productAvailability = null,
        public readonly ?array $errors = null,
        public readonly ?string $message = null
    ) {}

    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'productAvailability' => $this->productAvailability?->toArray(),
            'errors' => $this->errors,
            'message' => $this->message,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            success: !self::hasField($data, 'errors') || empty(self::getField($data, 'errors')),
            productAvailability: self::createFromArrayIfExists(
                ProductAvailabilityDTO::class,
                $data,
                'productAvailability'
            ),
            errors: self::getField($data, 'errors'),
            message: self::getField($data, 'message')
        );
    }
}
