<?php

namespace App\DTOs\Sabre;

class AvailabilityRequestDTO
{
    public function __construct(
        public readonly string $primaryChannel,
        public readonly string $secondaryChannel,
        public readonly int $chainId,
        public readonly int $hotelId,
        public readonly string $startDate,
        public readonly string $endDate,
        public readonly int $numRooms,
        public readonly int $adults,
        public readonly ?int $children = null,
        public readonly ?string $childrenAge = null,
        public readonly ?string $loyaltyProgram = null,
        public readonly ?string $loyaltyLevel = null,
        public readonly string $content = 'full',
        public readonly ?bool $onlyCheckRequested = null,
        public readonly ?string $sortProducts = null,
        public readonly ?string $accessType = null,
        public readonly ?string $accessCode = null,
        public readonly ?string $roomCode = null,
        public readonly ?string $rateCode = null,
        public readonly ?string $roomClass = null,
        public readonly ?string $roomClassExcluded = null,
        public readonly ?string $rateFilter = null,
        public readonly ?string $priceRangeCurrency = null,
        public readonly ?string $crsConfirmationNumber = null
    ) {}

    public function toArray(): array
    {
        $params = [
            'primaryChannel' => $this->primaryChannel,
            'secondaryChannel' => $this->secondaryChannel,
            'chainId' => $this->chainId,
            'hotelId' => $this->hotelId,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'numRooms' => $this->numRooms,
            'adults' => $this->adults,
            'content' => $this->content,
        ];

        // 添加可选参数
        if ($this->children !== null) {
            $params['children'] = $this->children;
        }
        if ($this->childrenAge !== null) {
            $params['childrenAge'] = $this->childrenAge;
        }
        if ($this->loyaltyProgram !== null) {
            $params['loyaltyProgram'] = $this->loyaltyProgram;
        }
        if ($this->loyaltyLevel !== null) {
            $params['loyaltyLevel'] = $this->loyaltyLevel;
        }
        if ($this->onlyCheckRequested !== null) {
            $params['onlyCheckRequested'] = $this->onlyCheckRequested ? 'true' : 'false';
        }
        if ($this->sortProducts !== null) {
            $params['sortProducts'] = $this->sortProducts;
        }
        if ($this->accessType !== null) {
            $params['accessType'] = $this->accessType;
        }
        if ($this->accessCode !== null) {
            $params['accessCode'] = $this->accessCode;
        }
        if ($this->roomCode !== null) {
            $params['roomCode'] = $this->roomCode;
        }
        if ($this->rateCode !== null) {
            $params['rateCode'] = $this->rateCode;
        }
        if ($this->roomClass !== null) {
            $params['roomClass'] = $this->roomClass;
        }
        if ($this->roomClassExcluded !== null) {
            $params['roomClassExcluded'] = $this->roomClassExcluded;
        }
        if ($this->rateFilter !== null) {
            $params['rateFilter'] = $this->rateFilter;
        }
        if ($this->priceRangeCurrency !== null) {
            $params['priceRangeCurrency'] = $this->priceRangeCurrency;
        }
        if ($this->crsConfirmationNumber !== null) {
            $params['crsConfirmationNumber'] = $this->crsConfirmationNumber;
        }

        return $params;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            primaryChannel: $data['primaryChannel'],
            secondaryChannel: $data['secondaryChannel'],
            chainId: $data['chainId'],
            hotelId: $data['hotelId'],
            startDate: $data['startDate'],
            endDate: $data['endDate'],
            numRooms: $data['numRooms'],
            adults: $data['adults'],
            children: $data['children'] ?? null,
            childrenAge: $data['childrenAge'] ?? null,
            loyaltyProgram: $data['loyaltyProgram'] ?? null,
            loyaltyLevel: $data['loyaltyLevel'] ?? null,
            content: $data['content'] ?? 'full',
            onlyCheckRequested: isset($data['onlyCheckRequested']) ? filter_var($data['onlyCheckRequested'], FILTER_VALIDATE_BOOLEAN) : null,
            sortProducts: $data['sortProducts'] ?? null,
            accessType: $data['accessType'] ?? null,
            accessCode: $data['accessCode'] ?? null,
            roomCode: $data['roomCode'] ?? null,
            rateCode: $data['rateCode'] ?? null,
            roomClass: $data['roomClass'] ?? null,
            roomClassExcluded: $data['roomClassExcluded'] ?? null,
            rateFilter: $data['rateFilter'] ?? null,
            priceRangeCurrency: $data['priceRangeCurrency'] ?? null,
            crsConfirmationNumber: $data['crsConfirmationNumber'] ?? null
        );
    }
}
