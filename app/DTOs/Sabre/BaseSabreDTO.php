<?php

namespace App\DTOs\Sabre;

use Illuminate\Support\Arr;

/**
 * Sabre DTOs 的基类
 * 提供通用的数组访问和类型转换方法
 */
abstract class BaseSabreDTO
{
    /**
     * 将DTO转换为数组
     */
    abstract public function toArray(): array;

    /**
     * 从数组创建DTO实例
     */
    abstract public static function fromArray(array $data): self;

    /**
     * 将DTO转换为JSON字符串
     */
    public function toJson(int $options = 0): string
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * 从JSON字符串创建DTO实例
     */
    public static function fromJson(string $json): self
    {
        $data = json_decode($json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException('Invalid JSON: ' . json_last_error_msg());
        }

        return static::fromArray($data);
    }

    /**
     * 安全获取字段值，支持默认值
     */
    protected static function getField(array $data, string $field, $default = null)
    {
        return Arr::get($data, $field, $default);
    }

    /**
     * 检查字段是否存在
     */
    protected static function hasField(array $data, string $field): bool
    {
        return Arr::has($data, $field);
    }

    /**
     * 验证必需字段
     */
    protected static function validateRequiredFields(array $data, array $requiredFields): void
    {
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (!Arr::has($data, $field)) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            throw new \InvalidArgumentException(
                'Missing required fields: ' . implode(', ', $missingFields)
            );
        }
    }

    /**
     * 转换为字符串类型
     */
    protected static function toString($value, string $default = ''): string
    {
        return $value !== null ? (string) $value : $default;
    }

    /**
     * 转换为整数类型
     */
    protected static function toInt($value, int $default = 0): int
    {
        return $value !== null ? (int) $value : $default;
    }

    /**
     * 转换为浮点数类型
     */
    protected static function toFloat($value, float $default = 0.0): float
    {
        return $value !== null ? (float) $value : $default;
    }

    /**
     * 转换为布尔类型
     */
    protected static function toBool($value, bool $default = false): bool
    {
        return $value !== null ? (bool) $value : $default;
    }

    /**
     * 转换为数组类型
     */
    protected static function toArrayType($value, array $default = []): array
    {
        return is_array($value) ? $value : $default;
    }

    /**
     * 安全地从数组创建DTO实例，支持空值
     */
    protected static function createFromArrayIfExists(string $dtoClass, array $data, string $field): ?object
    {
        $fieldData = self::getField($data, $field);

        if (empty($fieldData) || !is_array($fieldData)) {
            return null;
        }

        return $dtoClass::fromArray($fieldData);
    }

    /**
     * 安全地从数组创建DTO数组
     */
    protected static function createArrayFromField(string $dtoClass, array $data, string $field): array
    {
        $fieldData = self::getField($data, $field, []);

        if (!is_array($fieldData)) {
            return [];
        }

        return array_map(
            fn($item) => is_array($item) ? $dtoClass::fromArray($item) : $item,
            $fieldData
        );
    }
}
