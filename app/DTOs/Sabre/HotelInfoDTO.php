<?php

namespace App\DTOs\Sabre;

class HotelInfoDTO
{
    public function __construct(
        public readonly ?int $id = null,
        public readonly ?string $code = null,
        public readonly ?string $name = null,
        public readonly ?string $brand = null,
        public readonly ?AddressInfoDTO $address = null,
        public readonly ?array $amenities = null,
        public readonly ?array $images = null
    ) {}

    public function toArray(): array
    {
        return [
            'Id' => $this->id,
            'Code' => $this->code,
            'Name' => $this->name,
            'Brand' => $this->brand,
            'Address' => $this->address?->toArray(),
            'Amenities' => $this->amenities,
            'Images' => $this->images,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['Id'] ?? null,
            code: $data['Code'] ?? null,
            name: $data['Name'] ?? null,
            brand: $data['Brand'] ?? null,
            address: isset($data['Address']) ? AddressInfoDTO::fromArray($data['Address']) : null,
            amenities: $data['Amenities'] ?? null,
            images: $data['Images'] ?? null
        );
    }
}
