<?php

namespace App\DTOs\Sabre;

class GuestCountDTO
{
    public function __construct(
        public readonly string $ageQualifyingCode,
        public readonly int $numGuests,
        public readonly ?array $ages = null
    ) {}

    public function toArray(): array
    {
        $data = [
            'AgeQualifyingCode' => $this->ageQualifyingCode,
            'NumGuests' => $this->numGuests,
        ];

        if ($this->ages !== null) {
            $data['Ages'] = $this->ages;
        }

        return $data;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            ageQualifyingCode: $data['AgeQualifyingCode'],
            numGuests: $data['NumGuests'],
            ages: $data['Ages'] ?? null
        );
    }
}
