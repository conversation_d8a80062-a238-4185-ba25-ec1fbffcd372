<?php

namespace App\DTOs\Sabre;

class ProductDetailsDTO
{
    public function __construct(
        public readonly string $rateCode,
        public readonly string $roomCode
    ) {}

    public function toArray(): array
    {
        return [
            'RateCode' => $this->rateCode,
            'RoomCode' => $this->roomCode,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            rateCode: $data['RateCode'],
            roomCode: $data['RoomCode']
        );
    }
}
