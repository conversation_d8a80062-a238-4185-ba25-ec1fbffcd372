<?php

namespace App\DTOs;

use Illuminate\Support\Arr;

abstract class BaseDTO
{
    /**
     * 将DTO转换为数组
     */
    abstract public function toArray(): array;

    /**
     * 从数组创建DTO实例
     */
    abstract public static function fromArray(array $data): self;

    /**
     * 将DTO转换为JSON字符串
     */
    public function toJson(int $options = 0): string
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * 从JSON字符串创建DTO实例
     */
    public static function fromJson(string $json): self
    {
        $data = json_decode($json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException('Invalid JSON: ' . json_last_error_msg());
        }

        return static::fromArray($data);
    }

    /**
     * 验证必需字段
     */
    protected static function validateRequiredFields(array $data, array $requiredFields): void
    {
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (!Arr::has($data, $field)) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            throw new \InvalidArgumentException(
                'Missing required fields: ' . implode(', ', $missingFields)
            );
        }
    }

    /**
     * 获取字段值，支持默认值
     */
    protected static function getField(array $data, string $field, $default = null)
    {
        return Arr::get($data, $field, $default);
    }

    /**
     * 转换为字符串类型
     */
    protected static function toString($value, string $default = ''): string
    {
        return $value !== null ? (string) $value : $default;
    }

    /**
     * 转换为整数类型
     */
    protected static function toInt($value, int $default = 0): int
    {
        return $value !== null ? (int) $value : $default;
    }

    /**
     * 转换为浮点数类型
     */
    protected static function toFloat($value, float $default = 0.0): float
    {
        return $value !== null ? (float) $value : $default;
    }

    /**
     * 转换为布尔类型
     */
    protected static function toBool($value, bool $default = false): bool
    {
        return $value !== null ? (bool) $value : $default;
    }

    /**
     * 转换为数组类型
     */
    protected static function toArrayType($value, array $default = []): array
    {
        return is_array($value) ? $value : $default;
    }
}
