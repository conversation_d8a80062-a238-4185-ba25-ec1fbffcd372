<?php

namespace App\Console\Commands;

use App\Services\GxpService;
use Illuminate\Console\Command;

class GxpTokenManagement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gxp:token
                            {action : 操作类型 (info|clear|refresh)}
                            {--username= : GXP用户名 (可选，默认使用配置文件)}
                            {--password= : GXP密码 (可选，默认使用配置文件)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'GXP token缓存管理命令';

    protected GxpService $gxpService;

    /**
     * Create a new command instance.
     */
    public function __construct(GxpService $gxpService)
    {
        parent::__construct();
        $this->gxpService = $gxpService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'info':
                $this->showTokenInfo();
                break;
            case 'clear':
                $this->clearTokenCache();
                break;
            case 'refresh':
                $this->refreshToken();
                break;
            default:
                $this->error("不支持的操作: {$action}");
                $this->info('支持的操作: info, clear, refresh');
                return 1;
        }

        return 0;
    }

    /**
     * 显示token缓存信息
     */
    protected function showTokenInfo()
    {
        $username = $this->option('username') ?: config('gha.gxp.username');
        $password = $this->option('password') ?: config('gha.gxp.password');

        if (!$username || !$password) {
            $this->error('需要在配置文件中设置GXP凭据或提供 --username 和 --password 参数');
            return;
        }

        try {
            if ($this->gxpService->isInitialized()) {
                $info = $this->gxpService->getCurrentTokenCacheInfo();
            } else {
                $info = $this->gxpService->getTokenCacheInfo($username, $password);
            }

            if (!$info) {
                $this->info('没有找到缓存的token');
                $this->line('使用凭据: ' . $username);
                return;
            }

            $this->info('Token缓存信息:');
            $this->line('- 使用凭据: ' . $username);
            $this->line('- 是否有token: ' . ($info['has_token'] ? '是' : '否'));
            $this->line('- 过期时间: ' . ($info['expires_at'] ? date('Y-m-d H:i:s', $info['expires_at']) : 'N/A'));
            $this->line('- 剩余时间: ' . ($info['expires_in_seconds'] ? $info['expires_in_seconds'] . '秒' : 'N/A'));
            $this->line('- Token类型: ' . ($info['token_type'] ?? 'N/A'));

        } catch (\Exception $e) {
            $this->error('获取token信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 清除token缓存
     */
    protected function clearTokenCache()
    {
        $username = $this->option('username') ?: config('gha.gxp.username');
        $password = $this->option('password') ?: config('gha.gxp.password');

        if ($username && $password) {
            // 清除指定用户的token
            if ($this->gxpService->isInitialized() &&
                $username === config('gha.gxp.username') &&
                $password === config('gha.gxp.password')) {
                $result = $this->gxpService->clearCurrentTokenCache();
            } else {
                $result = $this->gxpService->clearTokenCache($username, $password);
            }

            if ($result) {
                $this->info("已清除用户 {$username} 的token缓存");
            } else {
                $this->error("清除用户 {$username} 的token缓存失败");
            }
        } else {
            // 清除所有token缓存
            if ($this->confirm('确定要清除所有GXP token缓存吗？')) {
                $result = $this->gxpService->clearAllTokenCache();
                if ($result) {
                    $this->info('已清除所有GXP token缓存');
                } else {
                    $this->error('清除所有GXP token缓存失败');
                }
            }
        }
    }

    /**
     * 刷新token
     */
    protected function refreshToken()
    {
        $username = $this->option('username') ?: config('gha.gxp.username');
        $password = $this->option('password') ?: config('gha.gxp.password');

        if (!$username || !$password) {
            $this->error('需要在配置文件中设置GXP凭据或提供 --username 和 --password 参数');
            return;
        }

        try {
            // 先清除缓存
            if ($this->gxpService->isInitialized() &&
                $username === config('gha.gxp.username') &&
                $password === config('gha.gxp.password')) {
                $this->gxpService->clearCurrentTokenCache();
            } else {
                $this->gxpService->clearTokenCache($username, $password);
            }

            // 重新获取token
            $tokenDto = $this->gxpService->getGuestToken($username, $password);

            if ($tokenDto->isSuccess()) {
                $this->info('Token刷新成功');
                $this->line('- 使用凭据: ' . $username);
                $this->line('- Token: ' . ($tokenDto->token ? '已获取' : '未获取'));
                $this->line('- 过期时间: ' . ($tokenDto->expiresIn ? $tokenDto->expiresIn . '秒' : 'N/A'));
            } else {
                $this->error('Token刷新失败: ' . ($tokenDto->error ?? '未知错误'));
            }

        } catch (\Exception $e) {
            $this->error('刷新token失败: ' . $e->getMessage());
        }
    }
}
