<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\GxpService;
use Illuminate\Console\Command;

class TestGxpServices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gxp:test {--user-id=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试GXP服务功能';

    protected GxpService $gxpService;

    /**
     * Create a new command instance.
     */
    public function __construct(GxpService $gxpService)
    {
        parent::__construct();
        $this->gxpService = $gxpService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始测试GXP服务...');

        // 测试获取访客令牌
        $this->testGuestToken();

        // 测试汇率查询（需要用户）
        $userId = $this->option('user-id');
        $user = User::find($userId);

        if ($user) {
            $this->testCurrencyRate($user);
            $this->testUserBookings($user);
        } else {
            $this->warn("用户ID {$userId} 不存在，跳过需要认证的测试");
        }

        $this->info('GXP服务测试完成！');
    }

    /**
     * 测试获取访客令牌
     */
    protected function testGuestToken()
    {
        $this->info('测试获取访客令牌...');

        try {
            $tokenDto = $this->gxpService->getGuestToken('test_user', 'test_password');

            $this->line('令牌获取成功:');
            $this->line('- Token: ' . ($tokenDto->token ? '已获取' : '未获取'));
            $this->line('- Token Type: ' . ($tokenDto->tokenType ?? 'N/A'));
            $this->line('- Expires In: ' . ($tokenDto->expiresIn ?? 'N/A'));

            if ($tokenDto->error) {
                $this->warn('错误: ' . $tokenDto->error);
            }

        } catch (\Exception $e) {
            $this->error('获取访客令牌失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试汇率查询
     */
    protected function testCurrencyRate(User $user)
    {
        $this->info('测试汇率查询...');

        try {
            $result = $this->gxpService->getCurrencyExchangeRate(
                'USD',
                '2025-01-01',
                5
            );

            $this->line('汇率查询成功:');
            $this->line('- 汇率记录数: ' . count($result['rates']));

            foreach ($result['rates'] as $rate) {
                $this->line("  - {$rate->currency}: {$rate->exchangeRate} ({$rate->effectiveDate})");
            }

        } catch (\Exception $e) {
            $this->error('汇率查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试用户预订查询
     */
    protected function testUserBookings(User $user)
    {
        $this->info('测试用户预订查询...');

        try {
            $result = $this->gxpService->getUserBookings([
                'page' => 0,
                'size' => 10,
                'status' => ['CURRENT', 'UPCOMING']
            ]);

            $this->line('预订查询成功:');
            $this->line('- 预订记录数: ' . count($result['reservations']));
            $this->line('- 总记录数: ' . ($result['total'] ?? 0));

            foreach ($result['reservations'] as $reservation) {
                $this->line("  - {$reservation->itineraryNumber}: {$reservation->hotelName} ({$reservation->status})");
            }

        } catch (\Exception $e) {
            $this->error('预订查询失败: ' . $e->getMessage());
        }
    }
}
