<?php

namespace App\Http\Controllers\V1;

use App\Services\GxpService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GxpController extends BaseController
{
    protected GxpService $gxpService;

    /**
     * 构造函数 - 依赖注入GxpService
     *
     * @param GxpService $gxpService
     */
    public function __construct(GxpService $gxpService)
    {
        $this->gxpService = $gxpService;
    }

    /**
     * 获取汇率转换
     * GET /api/v1/ddollars/exchange-rate
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCurrencyConversionRate(Request $request)
    {
        try {
            $this->validate($request->all(), [
                'currency' => 'required|string',
                'start_date' => 'required|date',
                'size' => 'sometimes|integer|min:1|max:100'
            ]);
            // 直接调用服务，凭据从配置文件自动读取
            $result = $this->gxpService->getCurrencyExchangeRate(
                $request->currency,
                $request->start_date,
                $request->size ?? 10
            );

            return successResponse($result, '获取汇率成功');

        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    /**
     * 获取访客令牌
     * POST /api/v1/auth/token
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGuestToken(Request $request)
    {
        try {
            $this->validate($request->all(), [
                'type' => 'required|string|in:GUEST,PARTNER',
                'username' => 'required|string',
                'password' => 'required|string'
            ]);

            // 直接调用getGuestToken方法获取token
            $tokenDto = $this->gxpService->getGuestToken(
                $request->username,
                $request->password,
                $request->type
            );

            return successResponse($tokenDto->toArray(), '获取访客令牌成功');

        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    /**
     * 预订仓库提交
     * POST /api/v1/reservations-repo
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reserveRepoPosting(Request $request)
    {
        try {
            $this->validate($request->all(), [
                'itinerary_number' => 'required|string',
                'status' => 'required|string|in:CREATE,MODIFY,CANCEL',
                'source' => 'sometimes|string',
                'chain_id' => 'required|string',
                'hotel_id' => 'required|string',
                'reservation_details' => 'required|array',
                'reservation_details.*.confirmation_number' => 'required|string',
                'reservation_details.*.member_rate_yn' => 'required|boolean',
                'profile_id' => 'sometimes|string' // PARTNER类型用户需要
            ]);

            // 验证用户是否已登录到我们的系统
            $user = Auth::user();
            if (!$user) {
                return errorResponse('未授权访问', 401);
            }

            $reservationData = $request->only([
                'itinerary_number', 'status', 'source', 'chain_id',
                'hotel_id', 'reservation_details', 'profile_id'
            ]);

            // 直接调用服务，凭据从配置文件自动读取
            $result = $this->gxpService->submitReservation($reservationData);

            return successResponse($result, '预订提交成功');

        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }

    /**
     * 获取我的预订
     * GET /api/v1/reservations-repo
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMyBookings(Request $request)
    {
        try {
            $this->validate($request->all(), [
                'page' => 'sometimes|integer|min:0',
                'size' => 'sometimes|integer|min:1|max:100',
                'status' => 'sometimes|array',
                'status.*' => 'string|in:CURRENT,UPCOMING,PAST,CANCELED',
                'sort_by' => 'sometimes|string|in:ArrivalDate',
                'sort_direction' => 'sometimes|string|in:asc,desc',
                'profile_id' => 'sometimes|string' // PARTNER类型用户需要
            ]);

            // 验证用户是否已登录到我们的系统
            $user = Auth::user();
            if (!$user) {
                return errorResponse('未授权访问', 401);
            }

            $filters = $request->only([
                'page', 'size', 'status', 'sort_by', 'sort_direction', 'profile_id'
            ]);

            // 直接调用服务，凭据从配置文件自动读取
            $result = $this->gxpService->getUserBookings($filters);

            return successResponse($result, '获取预订列表成功');

        } catch (\Exception $exception) {
            return errorResponse($exception->getMessage());
        }
    }
}
