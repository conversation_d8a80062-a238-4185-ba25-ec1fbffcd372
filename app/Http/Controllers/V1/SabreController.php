<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use App\Services\SabreAvailabilityService;
use App\Services\SabreReservationService;
use App\Services\SabreHotelService;
use App\Exceptions\SabreApiException;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SabreController extends Controller
{
    protected SabreAvailabilityService $availabilityService;
    protected SabreReservationService $reservationService;
    protected SabreHotelService $hotelService;

    public function __construct(
        SabreAvailabilityService $availabilityService,
        SabreReservationService $reservationService,
        SabreHotelService $hotelService
    ) {
        $this->availabilityService = $availabilityService;
        $this->reservationService = $reservationService;
        $this->hotelService = $hotelService;
    }

    /**
     * 查询酒店可用性
     */
    public function checkAvailability(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'hotelId' => 'required|integer',
            'startDate' => 'required|date|after:today',
            'endDate' => 'required|date|after:startDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAge' => 'string|regex:/^(\d{1,2})(,\d{1,2})*(;\d{1,2}(,\d{1,2})*)*$/',
            'loyaltyProgram' => 'string',
            'loyaltyLevel' => 'string',
            'accessType' => 'string|in:Promotion,Group,Corporate',
            'accessCode' => 'string',
            'onlyPromo' => 'boolean',
            'format' => 'string|in:sabre,standard', // 响应格式：sabre原始格式或standard标准格式
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        // 验证儿童年龄与儿童数量是否匹配
        if ($request->has('children') && $request->has('childrenAge')) {
            $childrenCount = $request->input('children', 0);
            $childrenAge = $request->input('childrenAge', '');

            if ($childrenCount > 0 && !empty($childrenAge)) {
                $ageValidation = $this->validateChildrenAge($childrenCount, $childrenAge, $request->input('numRooms', 1));
                if (!$ageValidation['valid']) {
                    return errorResponse($ageValidation['message']);
                }
            }
        }

        try {
            $params = $request->all();
            $format = $request->input('format', 'standard'); // 默认使用标准格式

            // 根据参数选择不同的查询方法
            if ($format === 'standard') {
                // 返回标准格式
                if ($request->boolean('onlyPromo') && !empty($params['accessCode'])) {
                    $result = $this->availabilityService->checkPromoRateOnlyStandard($params);
                } elseif (!empty($params['accessCode'])) {
                    $result = $this->availabilityService->checkMemberAndNonMemberRatesWithPromoStandard($params);
                } elseif (($params['numRooms'] ?? 1) > 1) {
                    $result = $this->availabilityService->checkMultiRoomAvailabilityStandard($params);
                } else {
                    $result = $this->availabilityService->checkMemberAndNonMemberRatesStandard($params);
                }

                return successResponse($result->toArray());
            } else {
                // 返回Sabre原始格式
                if ($request->boolean('onlyPromo') && !empty($params['accessCode'])) {
                    $result = $this->availabilityService->checkPromoRateOnly($params);
                } elseif (!empty($params['accessCode'])) {
                    $result = $this->availabilityService->checkMemberAndNonMemberRatesWithPromo($params);
                } elseif (($params['numRooms'] ?? 1) > 1) {
                    $result = $this->availabilityService->checkMultiRoomAvailability($params);
                } else {
                    $result = $this->availabilityService->checkMemberAndNonMemberRates($params);
                }

                $parsed = $this->availabilityService->parseAvailabilityResponse($result);

                return successResponse($parsed);
            }

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 创建预订
     */
    public function createReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'hotelId' => 'required|integer',
            'chainId' => 'integer',
            'guests' => 'required|array|min:1',
            'guests.*.PersonName.GivenName' => 'required|string',
            'guests.*.PersonName.Surname' => 'required|string',
            'guests.*.EmailAddress' => 'required|array|min:1',
            'guests.*.ContactNumbers' => 'required|array|min:1',
            'guests.*.Locations' => 'required|array|min:1',
            'guests.*.Payments' => 'required|array|min:1',
            'roomStay' => 'required|array',
            'roomStay.StartDate' => 'required|date',
            'roomStay.EndDate' => 'required|date|after:roomStay.StartDate',
            'roomStay.NumRooms' => 'required|integer|min:1',
            'roomStay.GuestCount' => 'required|array|min:1',
            'roomStay.Products' => 'required|array|min:1',
            'loyaltyMemberships' => 'array',
            'promotion' => 'array',
            'isMultiRoom' => 'boolean',
            'withMembership' => 'boolean',
            'withPromo' => 'boolean',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        try {
            $params = $request->all();

            // 根据参数选择不同的创建方法
            if ($request->boolean('isMultiRoom')) {
                $result = $this->reservationService->createMultiRoomBooking($params);
            } elseif ($request->boolean('withMembership') && $request->boolean('withPromo')) {
                $result = $this->reservationService->createSingleRoomBookingWithMembershipAndPromo($params);
            } elseif ($request->boolean('withMembership')) {
                $result = $this->reservationService->createSingleRoomBookingWithMembership($params);
            } elseif ($request->boolean('withPromo')) {
                $result = $this->reservationService->createSingleRoomBookingNonMemberWithPromo($params);
            } else {
                $result = $this->reservationService->createSingleRoomBookingNonMember($params);
            }

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 查询预订
     */
    public function getReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'itineraryNumber' => 'integer',
            'confirmationNumber' => 'string',
            'hotelId' => 'required_with:confirmationNumber|integer',
            'chainId' => 'integer',
            'channel' => 'string',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        if (!$request->has('itineraryNumber') && !$request->has('confirmationNumber')) {
            return errorResponse('必须提供行程号或确认号', 400);
        }

        try {
            $result = $this->reservationService->getReservation($request->all());

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 修改预订
     */
    public function modifyReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'Reservations' => 'required|array|min:1',
            'Reservations.*.CRS_confirmationNumber' => 'required|string',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        try {
            $result = $this->reservationService->modifyReservation($request->all());

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 取消预订
     */
    public function cancelReservation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'confirmationNumber' => 'required|string',
            'hotelId' => 'required|integer',
            'hotelCode' => 'string',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }

        try {
            $result = $this->reservationService->cancelReservation(
                $request->input('confirmationNumber'),
                $request->input('hotelId'),
                $request->input('hotelCode')
            );

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 获取酒店详情
     */
    public function getHotelDetails(Request $request, int $hotelId): JsonResponse
    {
        try {
            $options = [];
            if ($request->has('include')) {
                $options['include'] = $request->input('include');
            }

            $result = $this->hotelService->getHotelDetails($hotelId, $options);

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 获取酒店支付方式
     */
    public function getHotelPaymentMethods(int $hotelId): JsonResponse
    {
        try {
            $result = $this->hotelService->getHotelPaymentMethods($hotelId);

            return successResponse($result);

        } catch (SabreApiException $e) {
            return errorResponse($e->getUserMessage(), $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        } catch (\Exception $e) {
            return errorResponse('系统错误，请稍后再试', 500);
        }
    }

    /**
     * 清除缓存
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            $type = $request->input('type', 'all');
            $hotelId = $request->input('hotelId');

            switch ($type) {
                case 'availability':
                    $this->availabilityService->clearAvailabilityCache($request->all());
                    break;
                case 'hotel':
                    $this->hotelService->clearHotelDetailsCache($hotelId);
                    break;
                case 'all':
                default:
                    $this->availabilityService->clearAvailabilityCache();
                    $this->hotelService->clearHotelDetailsCache();
                    break;
            }

            return successResponse();

        } catch (\Exception $e) {
            return errorResponse('清除缓存失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 验证儿童年龄格式和数量
     */
    protected function validateChildrenAge(int $childrenCount, string $childrenAge, int $numRooms): array
    {
        // 按房间分割年龄字符串
        $roomAges = explode(',', $childrenAge);

        $totalAgeCount = 0;
        foreach ($roomAges as $roomIndex => $roomAgeStr) {
            if (empty($roomAgeStr)) {
                continue;
            }

            $ages = explode(',', $roomAgeStr);
            foreach ($ages as $age) {
                $age = trim($age);

                // 验证年龄是否为数字
                if (!is_numeric($age)) {
                    return [
                        'valid' => false,
                        'message' => "无效的年龄格式: {$age}"
                    ];
                }

                $ageInt = (int)$age;

                // 验证年龄范围
                if ($ageInt < 0 || $ageInt > 17) {
                    return [
                        'valid' => false,
                        'message' => "儿童年龄必须在0-17岁之间，当前: {$ageInt}岁"
                    ];
                }

                $totalAgeCount++;
            }
        }

        // 验证总年龄数量是否与儿童数量匹配
        if ($totalAgeCount != $childrenCount) {
            return [
                'valid' => false,
                'message' => "儿童数量({$childrenCount})与提供的年龄数量({$totalAgeCount})不匹配"
            ];
        }

        return ['valid' => true, 'message' => ''];
    }
}
