<?php

use Illuminate\Support\Facades\Log;

if (! function_exists('successResponse')) {
    /**
     * 返回成功
     * @param mixed|null $data
     * @param string $message
     * @param int $status
     * @return \Illuminate\Http\JsonResponse
     */
    function successResponse(mixed $data = null, string $message = 'Success', int $status = 200): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => $message,
            'status_code' => $status,
        ], $status);
    }
}

if (! function_exists('errorResponse')) {
    /**
     * 返回失败
     * @param string $message
     * @param int $status
     * @param mixed|null $data
     * @return \Illuminate\Http\JsonResponse
     */
    function errorResponse(string $message = 'Error', int $status = 400, mixed $data = null): \Illuminate\Http\JsonResponse
    {
        // 校验状态码是否合法
        if ($status < 100 || $status >= 600) {
            $status = 500;
        }
        return response()->json([
            'success' => false,
            'data' => $data,
            'message' => $message,
            'status_code' => $status,
        ], 200);
    }
}

if (! function_exists('encodeForDatabase')) {
   function encodeForDatabase($data, bool $throwException = false)
   {
       // 处理空值
       if (blank($data)) {
           return null;
       }
       // 如果已经是JSON字符串且有效，直接返回
       if (is_string($data) && isValidJson($data)) {
           return $data;
       }
       try {
           // 编码为JSON，不转义斜杠和Unicode字符
           $json = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
           if ($json === false) {
               throw new \RuntimeException('JSON encoding failed: ' . json_last_error_msg());
           }
           return $json;
       } catch (\Exception $e) {
           Log::error('JSON编码失败', [
               'data' => $data,
               'error' => $e->getMessage()
           ]);

           if ($throwException) {
               throw new \InvalidArgumentException("JSON编码失败: " . $e->getMessage());
           }
           return null;
       }
   }
}
if (! function_exists('isValidJson')) {
    function isValidJson(?string $json): bool
    {
        if (!is_string($json) || trim($json) === '') {
            return false;
        }
        json_decode($json);
        return json_last_error() === JSON_ERROR_NONE;
    }
}

if (!function_exists('toCent')) {
    /**
     * 将金额（元）转换为整数（分）
     *
     * @param mixed $value
     * @return int
     */
    function toCent($value): int
    {
        // 处理 null / string / int / float 等情况
        $clean = str_replace(',', '', (string) $value); // 可处理 "1,234.56"
        return (int) bcmul($clean, '100', 0);
    }
}
