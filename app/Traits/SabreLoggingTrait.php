<?php

namespace App\Traits;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

trait SabreLoggingTrait
{
    /**
     * 记录Sabre API请求日志
     */
    protected function logSabreRequest(string $endpoint, array $requestData, array $headers = [], string $method = 'POST'): string
    {
        $requestId = $this->generateRequestId();

        $logData = [
            'request_id' => $requestId,
            'endpoint' => $endpoint,
            'method' => $method,
            'timestamp' => now()->toISOString(),
            'request_data' => $requestData,
            'headers' => $this->sanitizeHeaders($headers),
        ];

        // 根据配置决定日志级别
        $logLevel = config('sabre.logging.level', 'info');

        Log::channel(config('sabre.logging.channel', 'daily'))
           ->log($logLevel, '[SABRE REQUEST] ' . $endpoint, $logData);

        return $requestId;
    }

    /**
     * 记录Sabre API响应日志
     */
    protected function logSabreResponse(string $requestId, string $endpoint, array $responseData, int $statusCode = 200, float $duration = 0): void
    {
        $logData = [
            'request_id' => $requestId,
            'endpoint' => $endpoint,
            'status_code' => $statusCode,
            'duration_ms' => round($duration * 1000, 2),
            'timestamp' => now()->toISOString(),
            'response_data' => json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
            'response_size' => strlen(json_encode($responseData)),
        ];

        // 根据状态码决定日志级别
        $logLevel = $statusCode >= 400 ? 'error' : config('sabre.logging.level', 'info');

        Log::channel(config('sabre.logging.channel', 'daily'))
           ->log($logLevel, '[SABRE RESPONSE] ' . $endpoint, $logData);
    }

    /**
     * 记录Sabre API错误日志
     */
    protected function logSabreError(string $requestId, string $endpoint, \Throwable $exception, array $context = []): void
    {
        $logData = [
            'request_id' => $requestId,
            'endpoint' => $endpoint,
            'error_type' => get_class($exception),
            'error_message' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ];

        // 如果是调试模式，添加堆栈跟踪
        if (config('app.debug')) {
            $logData['stack_trace'] = $exception->getTraceAsString();
        }

        Log::channel(config('sabre.logging.channel', 'daily'))
           ->error('[SABRE ERROR] ' . $endpoint, $logData);
    }

    /**
     * 记录缓存命中日志
     */
    protected function logSabreCacheHit(string $endpoint, string $cacheKey, array $context = []): void
    {
        if (!config('sabre.logging.cache_hits', false)) {
            return;
        }

        $logData = [
            'endpoint' => $endpoint,
            'cache_key' => $cacheKey,
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ];

        Log::channel(config('sabre.logging.channel', 'daily'))
           ->debug('[SABRE CACHE HIT] ' . $endpoint, $logData);
    }

    /**
     * 记录缓存未命中日志
     */
    protected function logSabreCacheMiss(string $endpoint, string $cacheKey, array $context = []): void
    {
        if (!config('sabre.logging.cache_misses', false)) {
            return;
        }

        $logData = [
            'endpoint' => $endpoint,
            'cache_key' => $cacheKey,
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ];

        Log::channel(config('sabre.logging.channel', 'daily'))
           ->debug('[SABRE CACHE MISS] ' . $endpoint, $logData);
    }

    /**
     * 生成请求ID
     */
    protected function generateRequestId(): string
    {
        return 'sabre_' . Str::random(12) . '_' . time();
    }

    /**
     * 清理敏感数据
     */
    protected function sanitizeLogData(array $data): array
    {
        $sensitiveFields = [
            'password',
            'token',
            'api_key',
            'apiKey',
            'authorization',
            'cardNumber',
            'CardNumber',
            'cvv',
            'CVV',
            'expireDate',
            'ExpireDate',
            'ssn',
            'SSN',
        ];

        return $this->recursiveSanitize($data, $sensitiveFields);
    }

    /**
     * 清理请求头中的敏感信息
     */
    protected function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = [
            'authorization',
            'Authorization',
            'x-api-key',
            'X-API-KEY',
            'cookie',
            'Cookie',
        ];

        $sanitized = [];
        foreach ($headers as $key => $value) {
            if (in_array($key, $sensitiveHeaders, true)) {
                $sanitized[$key] = $this->maskSensitiveValue($value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * 递归清理敏感数据
     */
    protected function recursiveSanitize(array $data, array $sensitiveFields): array
    {

        return $data;
    }

    /**
     * 掩码敏感值
     */
    protected function maskSensitiveValue($value): string
    {
        if (empty($value)) {
            return '[EMPTY]';
        }

        $valueStr = (string) $value;
        $length = strlen($valueStr);

        if ($length <= 4) {
            return str_repeat('*', $length);
        }

        // 显示前2位和后2位，中间用*代替
        return substr($valueStr, 0, 2) . str_repeat('*', $length - 4) . substr($valueStr, -2);
    }

    /**
     * 记录性能指标
     */
    protected function logSabrePerformance(string $endpoint, float $duration, int $responseSize, array $metrics = []): void
    {
        if (!config('sabre.logging.performance', false)) {
            return;
        }

        $logData = [
            'endpoint' => $endpoint,
            'duration_ms' => round($duration * 1000, 2),
            'response_size_bytes' => $responseSize,
            'timestamp' => now()->toISOString(),
            'metrics' => $metrics,
        ];

        // 如果响应时间超过阈值，记录为警告
        $slowThreshold = config('sabre.logging.slow_threshold_ms', 5000);
        $logLevel = ($duration * 1000) > $slowThreshold ? 'warning' : 'info';

        Log::channel(config('sabre.logging.channel', 'daily'))
           ->log($logLevel, '[SABRE PERFORMANCE] ' . $endpoint, $logData);
    }

    /**
     * 检查是否启用日志记录
     */
    protected function isSabreLoggingEnabled(): bool
    {
        return config('sabre.logging.enabled', true);
    }

    /**
     * 获取日志上下文信息
     */
    protected function getSabreLogContext(): array
    {
        return [
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'environment' => app()->environment(),
        ];
    }
}
