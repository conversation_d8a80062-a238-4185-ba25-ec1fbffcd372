<?php

namespace App\Logging;

use Monolog\Formatter\JsonFormatter;
use Monolog\LogRecord;

class <PERSON>breLogFormatter extends JsonFormatter
{
    /**
     * 最大嵌套深度
     */
    protected int $maxDepth = 6;

    /**
     * 最大数组项目数
     */
    protected int $maxItems = 50;

    /**
     * 最大字符串长度
     */
    protected int $maxStringLength = 2000;

    public function __construct(int $batchMode = self::BATCH_MODE_JSON, bool $appendNewline = true)
    {
        parent::__construct($batchMode, $appendNewline);
        
        // 设置最大嵌套深度，防止 "Over 9 levels deep" 错误
        $this->setMaxNormalizeDepth($this->maxDepth);
        $this->setMaxNormalizeItemCount($this->maxItems);
    }

    /**
     * 格式化日志记录
     */
    public function format(LogRecord $record): string
    {
        // 预处理上下文数据
        if (isset($record->context)) {
            $record = $record->with(context: $this->normalizeContext($record->context));
        }

        // 预处理额外数据
        if (isset($record->extra)) {
            $record = $record->with(extra: $this->normalizeContext($record->extra));
        }

        return parent::format($record);
    }

    /**
     * 规范化上下文数据
     */
    protected function normalizeContext(array $context, int $depth = 0): array
    {
        if ($depth >= $this->maxDepth) {
            return ['[MAX_DEPTH_REACHED]' => 'Data truncated due to depth limit'];
        }

        $normalized = [];
        $count = 0;

        foreach ($context as $key => $value) {
            if ($count >= $this->maxItems) {
                $normalized['[TRUNCATED]'] = "Array truncated, showing first {$this->maxItems} items";
                break;
            }

            $normalized[$key] = $this->normalizeValue($value, $depth);
            $count++;
        }

        return $normalized;
    }

    /**
     * 规范化单个值
     */
    protected function normalizeValue($value, int $depth = 0)
    {
        if ($depth >= $this->maxDepth) {
            return '[MAX_DEPTH_REACHED]';
        }

        if (is_array($value)) {
            return $this->normalizeContext($value, $depth + 1);
        }

        if (is_object($value)) {
            // 处理对象
            if (method_exists($value, 'toArray')) {
                return $this->normalizeContext($value->toArray(), $depth + 1);
            }

            if (method_exists($value, '__toString')) {
                $stringValue = (string) $value;
                return $this->truncateString($stringValue);
            }

            // 转换为数组
            $arrayValue = json_decode(json_encode($value), true);
            if (is_array($arrayValue)) {
                return $this->normalizeContext($arrayValue, $depth + 1);
            }

            return '[OBJECT:' . get_class($value) . ']';
        }

        if (is_string($value)) {
            return $this->truncateString($value);
        }

        if (is_resource($value)) {
            return '[RESOURCE:' . get_resource_type($value) . ']';
        }

        return $value;
    }

    /**
     * 截断长字符串
     */
    protected function truncateString(string $value): string
    {
        if (strlen($value) > $this->maxStringLength) {
            return substr($value, 0, $this->maxStringLength) . '...[TRUNCATED]';
        }

        return $value;
    }

    /**
     * 设置最大嵌套深度
     */
    public function setMaxDepth(int $maxDepth): self
    {
        $this->maxDepth = $maxDepth;
        $this->setMaxNormalizeDepth($maxDepth);
        return $this;
    }

    /**
     * 设置最大数组项目数
     */
    public function setMaxItems(int $maxItems): self
    {
        $this->maxItems = $maxItems;
        $this->setMaxNormalizeItemCount($maxItems);
        return $this;
    }

    /**
     * 设置最大字符串长度
     */
    public function setMaxStringLength(int $maxStringLength): self
    {
        $this->maxStringLength = $maxStringLength;
        return $this;
    }
}
